
## 物料和组织公共信息保存提交审核接口

### 接口描述：

- 广交云.【产品】.审核后 **推送** 金蝶云·星空旗舰版.【物料】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/gjwl_basedata_ext/saveMaterial

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/gjwl_basedata_ext/saveMaterial](https://gjwl.test.kdgalaxy.com/kapi/v2/sbd/bd_materialinventoryinfo/batchAdd)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|                                     |     |     |     |     |     |
|-------------------------------------| --- | --- | --- | --- | --- |
| **参数名**                             | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| materials                           | List&lt;MaterialDataVo&gt; | 否   | 物料数据 | 1   | \-  |
| materialInfoVo                      | MaterialInfoVo | 否   | 物料主档信息 | 2   | \-  |
|     id                              | String | 否   | 物料ID | 3   | "p5roQ" |
|     name                            | String | 是   | 物料名称 | 3   | "niHy7" |
|     number                          | String | 是   | 物料编码 | 3   | "IrZfm"，对应广交云的产品编码 |
|     gjwl_appid                      | String | 是   | 外部编码 | 1   | "IrZfm"，外部系统编码 |
|             gjwl_sourcesystemtype   | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
|     createorg                       | String | 是   | 创建组织(编码) | 3   | 固定值，传"gjwl" |
|     baseunit                        | String | 是   | 基本单位(编码) | 3   | "bUL2z" |
|     modelnum                        | String | 否   | 规格型号 | 3   | "Ocgcd" |
|     unitconvertdir                  | String | 否   | 换算方向 A正向换算 B逆向换算 | 3   | 固定值，传"A" |
| entry_groupstandard                 | List&lt;GroupStrandardEntry&gt; | 否   | 分类信息分录 | 3   | \-  |
|             groupStrandardCreateOrg | String | 否   | 分类标准创建组织(编码) | 4   | 固定值，传"gjwl" |
|             groupStrandardNumber    | String | 否   | 分类标准编码 | 4   | 固定值，传"JBFLBZ" |
|             groupNumber             | String | 否   | 分类编码 | 4   | 分类编码，广交云和星空维护一致 |
| commonInfoVo                        | CommonInfoVo | 否   | 物料组织公共信息 | 2   | \-  |
|     materialtype                    | String | 是   | 物料类型 1物资 7费用 8资产 9服务 | 3   | 固定值，传"1" |
|     materialattr                    | String | 是   | 物料属性 10030自制 10040外购 10050委外 10020虚拟 | 3   | 固定值，传"10040" |
|     enablepur                       | Boolean | 否   | 采购信息 | 3   | 固定值，传"true" |
|     enablesale                      | Boolean | 否   | 销售信息 | 3   | 固定值，传"true" |
|     enableinv                       | Boolean | 否   | 库存信息 | 3   | 固定值，传"true" |
|     group                           | String | 否   | 存货类别 CHJZFL01-SYS原材料 CHJZFL02-SYS辅料 CHJZFL03-SYS自制半成品 CHJZFL04-SYS委外半成品 CHJZFL05-SYS产成品 CHJZFL06-SYS库存商品 | 3   | 固定值，传"CHJZFL06-SYS" |

**请求参数示例（沙箱环境）:**

{

"materials": \[

{

"commonInfoVo": {

"enablesale": true,

"enablepur": true,

"materialtype": "1",

"materialattr": "10040",

"enableinv": true,

"group": "CHJZFL01-SYS"

},

"materialInfoVo": {

"createorg": "gjwl",

"baseunit": "pcs",

"gjwl_appid": "Item-000000174",

"gjwl_sourcesystemtype": "广交云供应链管理系统",

"number": "Item-000000174",

"name": "测试物料174",

"unitconvertdir": "A",

"entry_groupstandard": \[

{

"groupStrandardNumber": "JBFLBZ",

"groupStrandardCreateOrg": "gjwl",

"groupNumber": "03"

}

\]

}

}

\]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| saveResult | "\[\]" | Array&lt;Map&gt; | 3   | 保存返回结果详细信息 |
| resultVo | "\[\]" | Array&lt;Map&gt; | 4   | 返回结果详细信息 |
| commonInfoId | "2274113676959082496" | String | 4   | 物料组织公共信息Id |
| materialId | "2274113675264583680" | String | 4   | 物料Id |
| materialNumber | "Item-000000124" | String | 4   | 物料编码 |
| errors | "\[\]" | Array&lt;Map&gt; | 4   | 错误信息数据 |
| submitResult | "\[\]" | Array&lt;Map&gt; | 3   | 提交返回结果详细信息 |
| number | "Item-000000124" | String | 4   | 物料编码 |
| billStatus | true/false | Boolean | 4   | 提交是否成功 |
| id  | "2274113675264583680" | String | 4   | 物料Id |
| errors | "\[\]" | Array&lt;Map&gt; | 4   | 错误信息数据 |
| auditResult | "\[\]" | Array&lt;Map&gt; | 3   | 提交返回结果详细信息 |
| number | "Item-000000124" | String | 4   | 物料编码 |
| billStatus | true/false | Boolean | 4   | 提交是否成功 |
| id  | "2274113675264583680" | String | 4   | 物料Id |
| errors | "\[\]" | Array&lt;Map&gt; | 4   | 错误信息数据 |
| **finalErrorCode** | **成功时为0，失败时会返回错误码如400** | **String** | **2** | **接口最终错误码，按该字段判断接口是否成功** |
| **finalMessage** | **成功时为空，失败时会返回错误信息如“操作失败”** | **String** | **2** | **接口调用最终错误信息，按该字段获取接口调用错误信息** |
| **finalStatus** | **true/false** | **Boolean** | **2** | **接口最终访问是否成功，按该字段判断接口是否成功** |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data": {

"result": {

"saveResult": {

"result": true,

"resultVo": \[

{

"commonInfoId": "2274687206952061952",

"materialNumber": "Item-000000131",

"success": true,

"materialId": "2274687203714059264",

"message": "",

"orgNumber": "gjwl",

"orgId": "100000"

}

\],

"desc": ""

},

"submitResult": \[

{

"number": "Item-000000131",

"billStatus": true,

"id": "2274687203714059264",

"errors": \[\]

}

\],

"auditResult": \[

{

"number": "Item-000000131",

"billStatus": true,

"id": "2274687203714059264",

"errors": \[\]

}

\]

}

},

"errorCode": "0",

"message": null,

"status": true

}

## 物料库存信息更新接口

### 接口描述：

- 广交云.【产品】.新增后，如物料需要开启保质期则需要调用该接口 **推送** 金蝶云·星空旗舰版.【物料】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/sbd/bd_materialinventoryinfo/batchUpdate

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/sbd/bd_materialinventoryinfo/batchUpdate

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |                                                      |     |     |
| --- | --- | --- |------------------------------------------------------| --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明**                                             | **层级** | **参数值** |
| enablelot | Boolean | 是   | 启用批号管理                                               | 1   | false |
| enableshelflifemgr | Boolean | 是   | 保质期管理                                                | 1   | false |
| shelflifeunit | String | 是   | 保质期单位 day:日, month:月, year:年                         | 1   | "day" |
| shelflife | Integer | 否   | 保质期                                                  | 1   | 90  |
| caldirection | String | 否   | 计算方向 1:按生产日计算到期日, 2:按到期日计算生产日, 3:相互计算, 4:互不计算        | 1   | 固定值，传"4" |
| startdatecaltype | String | 否   | 生产日计算方式 1:到期日-保质期, 2:到期日-保质期+1                       | 1   | 固定值，传"1" |
| calculationforenddate | String | 是   | 到期日计算方式 0:生产日期+保质期, 1:生产日期+保质期-1, 2:生产日期+保质期上个月的最后一天 | 1   | 固定值，传"0" |
| leadtimeunit | String | 是   | 提前期单位 day:日, month:月, year:年                         | 1   | "day" |
| masterid_number | String | 是   | 物料编码.编码                                              | 1   | "V8Olr" |
| createorg_number | String | 是   | 库存信息创建组织.编码                                          | 1   | 固定值，传"gjwl" |

**请求参数示例（沙箱环境）:**

{

"data": \[

{

"createorg_number": "gjwl",

"masterid_number": "Item-000000125",

"enablelot": "true",

"enableshelflifemgr": "true",

"shelflife": 90,

"leadtimeunit": "day",

"calculationforenddate": "0",

"shelflifeunit": "day",

"caldirection": "1"

}

\]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data": {

"failCount": "0",

"result": \[

{

"billIndex": 0,

"billStatus": true,

"errors": \[\],

"id": "2274142827766100992",

"keys": {

"createorg.number": "gjwl",

"masterid.number": "Item-000000125"

},

"number": "",

"type": "Update"

}

\],

"successCount": "1"

},

"errorCode": "0",

"message": null,

"status": true

}