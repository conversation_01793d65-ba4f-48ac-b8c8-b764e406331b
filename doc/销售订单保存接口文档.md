## 销售订单保存接口

### 接口描述：

- 广交云.【销售订单】.新增/修改操作.审核后 **推送** 金蝶云·星空旗舰版.【销售订单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/sm/sm_salorder/saveSalOrder

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/sm/sm_salorder/saveSalOrder](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| id  | String | 否   | id  | 1   | 修改时需传 |
| bizdate | Date | 是   | 订单日期 | 1   | "2022-09-02" |
| org_number | String | 是   | 销售组织.编码 | 1   | 固定值，传"1011" |
| customer_number | String | 是   | 订货客户.编码 | 1   | "CUS-001" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | 固定值，传"sm_SalesOrder_STD_BT_S" |
| biztype_number | String | 是   | 业务类型.编码(210-物料类销售) | 1   | 固定值，传"210" |
| settlecurrency_number | String | 否   | 结算币别.货币代码 | 1   | 固定值，传"CNY"(人民币) |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "XSDD-20250505-005"，传广交云订单编号 |
| &nbsp;       gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
| comment | String | 否   | 表头备注 | 1   | "备注文本" |
| istax | Boolean | 否   | 含税  | 1   | true |
| iswholediscount | Boolean | 否   | 录入整单折扣 | 1   | false |
| wholediscountamount | Decimal | 否   | 整单折扣额 | 1   | "782.81" |
| reccondition_number | String | 是   | 收款条件.编号<br><br>(<br><br>SKTJ-1001_SYS:货到收款<br><br>SKTJ-1002_SYS:预收30%，货到收款70%<br><br>SKTJ-1003_SYS:预收30%，开票30天后收款70%<br><br>SKTJ-1004_SYS:开票30天后收款<br><br>SKTJ-1005_SYS:开票60天后收款<br><br>SKTJ-1006_SYS:开票90天后收款<br><br>SKTJ-1007_SYS:月结30天<br><br>SKTJ-1008_SYS:按项目计划收款<br><br>) | 1   | "x80Bu"，待确认 |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| id  | String | 否   | 物料明细.id | 2   | 修改时需传 |
| &nbsp; linetype_number | String | 是   | 行类型.编码 | 2   | 固定值，传"010" |
| &nbsp; material_number | String | 是   | 物料编码.编码 | 2   | "ITEM-001"，传广交云产品编码 |
| &nbsp; unit_number | String | 是   | 销售单位.编码 | 2   | "kg" |
| &nbsp; e_stockorg_number | String | 是   | 发货组织.编码 | 2   | 固定值，传"1011" |
| &nbsp; entrysettleorg_number | String | 是   | 结算组织.编码 | 2   | 固定值，传"1011" |
| &nbsp; price | Decimal | 否   | 物料明细.单价 | 2   | "69.77" |
| &nbsp; priceandtax | Decimal | 否   | 物料明细.含税单价 | 2   | "484.79" |
| &nbsp; qty | Decimal | 是   | 物料明细.数量 | 2   | "5" |
| &nbsp; taxrateid_number | String | 否   | 税率.编码 | 2   | "V5"，广交云做映射，传对应编码 |
| &nbsp; taxamount | Decimal | 否   | 物料明细.税额 | 2   | "0.9" |
| &nbsp; discounttype | String | 否   | 物料明细.折扣方式 A:折扣率(%), B:单位折扣额, NULL:无 | 2   | "NULL" |
| &nbsp; discountrate | Decimal | 否   | 物料明细.单位折扣(率) | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; discountamount | Decimal | 否   | 物料明细.折扣额 | 2   | "0.9"，如折扣方式为折扣率(%)或单位折扣额时需传 |
| &nbsp; remark | String | 否   | 物料明细.备注 | 2   | "备注文本" |
| &nbsp; warehouse_number | String | 否   | 仓库.编码 | 2   | "WH-001"，广交云和星空数据维护一致 |
| &nbsp; amountandtax | Decimal | 否   | 物料明细.价税合计 | 2   | "975.00" |
| &nbsp; amount | Decimal | 否   | 物料明细.金额 | 2   | "135.46" |
| &nbsp; ispresent | Boolean | 否   | 物料明细.赠品 | 2   | 默认为false，金额为0传true |
| &nbsp; project_number | String | 否   | 项目编码.项目编码 | 2   | "IHUsI" |
| lotnumber | String | 否   | 物料明细.批号 | 2   | "IHUsI"，对应广交云批次 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data": \[

&nbsp;       {
sws
&nbsp;           "customer_number": "Cus-000004",

&nbsp;           "iswholediscount": false,

&nbsp;           "billtype_number": "sm_SalesOrder_STD_BT_S",

&nbsp;           "bizdate": "2025-08-06",

&nbsp;           "wholediscountamount": "0",

&nbsp;           "org_number": "1011",

&nbsp;           "comment": "备注文本",

&nbsp;           "settlecurrency_number": "CNY",

&nbsp;           "istax": true,

&nbsp;           "gjwl_thirdparty_billno": "XSDD-2025-08-06-007",

&nbsp;           "gjwl_sourcesystemtype": "广交云供应链管理系统",

&nbsp;           "biztype_number": "210",

&nbsp;           "billentry": \[

&nbsp;               {

&nbsp;                   "e_stockorg_number": "1011",

&nbsp;                   "entrysettleorg_number": "1011",

&nbsp;                   "taxrateid_number": "V5",

&nbsp;                   "taxamount": "1.90",

&nbsp;                   "remark": "备注文本",

&nbsp;                   "ispresent": false,

&nbsp;                   "unit_number": "pcs",

&nbsp;                   "price": "9.52",

&nbsp;                   "priceandtax": "10",

&nbsp;                   "amount": "38.10",

&nbsp;                   "linetype_number": "010",

&nbsp;                   "amountandtax": "40.00",

&nbsp;                   "discounttype": "B",

&nbsp;                   "discountrate": "2.00",                  

&nbsp;                   "discountamount": "10.00",

&nbsp;                   "warehouse_number": "CK-001",

&nbsp;                   "material_number": "Item-*********",

&nbsp;                   "qty": "5",

&nbsp;                   "lotnumber": "123",

&nbsp;                   "gjwl_product_lotno": "123"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| billStatus | true/false | Boolean | 3   | 单据状态 |
| id  | "2271924339223870464" | String | 3   | **单据Id，需记录，后续修改和下推接口需用** |
| number | "XSDD-20250505-005" | String | 3   | 单据编号 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

&nbsp;   "data": {

&nbsp;       "failCount": "0",

&nbsp;       "result": \[

&nbsp;           {

&nbsp;               "billIndex": 0,

&nbsp;               "billStatus": true,

&nbsp;               "errors": \[\],

&nbsp;               "id": "2275577405722607616",

&nbsp;               "keys": {

&nbsp;                   "billno": ""

&nbsp;               },

&nbsp;               "number": "XSDD-250806-000004",

&nbsp;               "type": "Add"

&nbsp;           }

&nbsp;       \],

&nbsp;       "successCount": "1"

&nbsp;   },

&nbsp;   "errorCode": "0",

&nbsp;   "message": null,

&nbsp;   "status": true

}