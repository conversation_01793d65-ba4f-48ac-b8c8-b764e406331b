
## 盘盈单保存接口

### 接口描述：

- 广交云.【报溢单】.审核后 **推送** 金蝶云·星空旗舰版.【盘盈单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/im_surplusbill/saveSurpluseBill

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/im_surplusbill/saveSurpluseBill](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| org_number | String | 是   | 库存组织.编码 | 1   | 固定值，传"1011" |
| billno | String | 否   | 单据编号 | 1   | "5Xrbr" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "PYD-20250505-005"，传广交云单号 |
| bookdate | Date | 是   | 记账日期 | 1   | "2023-11-15" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | 固定值，传"im_InvCheckIn_STD_BT_S" |
| biztype_number | String | 是   | 业务类型.编码 | 1   | "350"，固定值 |
| biztime | Date | 是   | 业务日期 | 1   | "2022-07-04" |
| invscheme_number | String | 是   | 库存事务.编码 | 1   | "350"，固定值 |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| linetype_number | String | 是   | 行类型.编码 | 2   | "010"，固定值 |
| material_number | String | 是   | 物料编码.编码 | 2   | "item-001"，广交产品编码 |
| invstatus_number | String | 是   | 入库库存状态.编码 | 2   | "110"，固定值 |
| invtype_number | String | 是   | 入库库存类型.编码 | 2   | "110"，固定值 |
| owner_number | String | 是   | 货主.编码 | 2   | 固定值，传"1011" |
| keeper_number | String | 是   | 保管者.编码 | 2   | 固定值，传"1011" |
| ownertype | String | 是   | 物料明细.货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org"，固定值 |
| keepertype | String | 是   | 物料明细.保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org"，固定值 |
| unit_number | String | 是   | 库存单位.编码 | 2   | "g" |
| warehouse_number | String | 是   | 仓库.编码 | 2   | "CK001" |
| qty | Decimal | 否   | 物料明细.盘点数量（库存） | 2   | "862.52"，不传 |
| invgainqty | Decimal | 是   | 物料明细.盘盈数量（库存） | 2   | "320.16"，传差异数量 |
| invqtyacc | Decimal | 否   | 物料明细.账存数量（库存） | 2   | "641.13"，<br><br>不传 |
| baseunit_number | String | 是   | 基本单位.编码 | 2   | "2lfGe"，同库存单位.编码 |
| baseqty | Decimal | 是   | 物料明细.盘点数量（基本） | 2   | "202.48"，同物料明细.盘点数量（库存） |
| lotnumber | String | 否   | 物料明细.批号 | 2   | "IHUsI"，对应广交云批次 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data": \[

&nbsp;       {

&nbsp;           "billno": "PYRK-250814-000002",

&nbsp;           "gjwl_thirdparty_billno": "PYRK-250814-000002",

&nbsp;           "biztime": "2025-08-14",

&nbsp;           "bookdate": "2025-08-14",

&nbsp;           "billtype_number": "im_InvCheckIn_STD_BT_S",

&nbsp;           "biztype_number": "350",

&nbsp;           "invscheme_number": "350",

&nbsp;           "org_number": "1011",

&nbsp;           "comment": "备注",

&nbsp;           "billentry": \[

&nbsp;               {

&nbsp;                   "linetype_number": "010",

&nbsp;                   "material_number": "Item-000000100",

&nbsp;                   "invstatus_number": "110",

&nbsp;                   "invtype_number": "110",

&nbsp;                   "owner_number": "1011",

&nbsp;                   "keeper_number": "1011",

&nbsp;                   "ownertype": "bos_org",

&nbsp;                   "keepertype": "bos_org",

&nbsp;                   "lotnumber": "123",

&nbsp;                   "gjwl_product_lotno": "123",

&nbsp;                   "unit_number": "pcs",

&nbsp;                   "baseunit_number": "pcs",

&nbsp;                   "warehouse_number": "CK-001",

&nbsp;                   "project_number": "KD-P-ALL_SYS",

&nbsp;                   "producedate": "2025-07-29",

&nbsp;                   "expirydate": "2029-07-29",

&nbsp;                   "qty": "10",

&nbsp;                   "baseqty": "10",

&nbsp;                   "invgainqty": "10",

&nbsp;                   "entrycomment": "单据体备注"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data":{

"failCount":"0",

"result":\[

{

"billIndex":0,

"billStatus":true,

"errors":\[\],

"id":"2068295602566871040",

"keys":{

"billno":"test1024002"

},

"number":"test1024002",

"type":"Add"

}

\],

"successCount":"1"

},

"errorCode":"0",

"message":null,

"status":true

}

## 盘亏单保存接口

### 接口描述：

- 广交云.【报损单(盘亏)】.审核后 **推送** 金蝶云·星空旗舰版.【盘亏单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/im_deficitbill/saveDeficitBill

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/im_deficitbill/saveDeficitBill](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| org_number | String | 是   | 库存组织.编码 | 1   | "e50Oh" |
| billno | String | 否   | 单据编号 | 1   | "5Xrbr" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "PYD-20250505-005"，传广交云单号 |
| bookdate | Date | 是   | 记账日期 | 1   | "2023-11-15" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | "im_InvCheckOut_STD_BT_S" |
| biztype_number | String | 是   | 业务类型.编码 | 1   | "351" |
| biztime | Date | 是   | 业务日期 | 1   | "2022-07-04" |
| invscheme_number | String | 是   | 库存事务.编码 | 1   | "351" |
| dept_number | String | 否   | 部门.编码 | 1   | "BM-001" |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| linetype_number | String | 是   | 行类型.编码 | 2   | "010" |
| material_number | String | 是   | 物料编码.编码 | 2   | "item-001" |
| outinvstatus_number | String | 是   | 出库库存状态.编码 | 2   | "110" |
| outinvtype_number | String | 是   | 出库库存类型.编码 | 2   | "110" |
| outowner_number | String | 是   | 货主.编码 | 2   | "baseline" |
| outkeeper_number | String | 是   | 保管者.编码 | 2   | "baseline" |
| outownertype | String | 是   | 物料明细.货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org" |
| outkeepertype | String | 是   | 物料明细.保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org" |
| unit_number | String | 是   | 库存单位.编码 | 2   | "g" |
| warehouse_number | String | 是   | 仓库.编码 | 2   | "CK001" |
| qty | Decimal | 否   | 物料明细.盘点数量（库存） | 2   | "862.52"，不传 |
| invqtyacc | Decimal | 否   | 物料明细.账存数量（库存） | 2   | "216.43"，不传 |
| invlossqty | Decimal | 是   | 物料明细.盘亏数量（库存） | 2   | "166.25"，传差异数量 |
| baseunit_number | String | 是   | 基本单位.编码 | 2   | "2lfGe" |
| baseqty | Decimal | 是   | 物料明细.盘点数量（基本） | 2   | "202.48" |
| lotnumber | String | 否   | 物料明细.批号 | 2   | "IHUsI"，对应广交云批次 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |

**请求参数示例（沙箱环境）:**

{

"data":\[

{

"billno":"PYRK-231012-000004",

"org_number":"gjwl",

"billtype_number":"im_InvCheckOut_STD_BT_S",

"biztype_number":"351",

"invscheme_number":"351",

"biztime":"2024-10-14",

"bookdate":"2024-10-14",

"billentry":\[

{

"material_number":"Item-00000001",

"unit_number":"g",

"baseunit_number":"g",

"warehouse_number":"CK-002",

"outinvstatus_number":"110",

"outinvtype_number":"110",

"outowner_number":"gjwl",

"outkeeper_number":"gjwl",

"linetype_number":"010",

"outownertype":"bos_org",

"outkeepertype":"bos_org",

"qty":"188.09",

"baseqty":"188.09",

"invqtyacc":"216.43",

"invlossqty":"166.25",

}

\]

}

\]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data":{

"failCount":"0",

"result":\[

{

"billIndex":0,

"billStatus":true,

"errors":\[\],

"id":"2068295602566871040",

"keys":{

"billno":"test1024002"

},

"number":"test1024002",

"type":"Add"

}

\],

"successCount":"1"

},

"errorCode":"0",

"message":null,

"status":true

}
