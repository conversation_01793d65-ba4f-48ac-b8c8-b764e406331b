## 付款申请接口

## 接口描述：

- 付款申请审批接口给广交云系统调用。
- 业务大类类型为3030001 货物采购付款 对接传送billtype为: ap_payapply_BT_S
- 业务大类： 3030002 货物销售 对接传送billtype为:ap_payapply_BT_Refund

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/ap/payApply/payApplyAutoCreate

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/ap/payApply/payApplyAutoCreate

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求参数示例（沙箱环境）:**

{

&nbsp;"data":\[

&nbsp; {

&nbsp;  "orgNum":"gjwl",

&nbsp;  "applyOrgNum":"gjwl",

&nbsp;  "payorgNum":"gjwl",

&nbsp;  "applyDate":"2024-10-17",

&nbsp;  "sourceSystem":"GF",

&nbsp;  "sourceNo":"GF0004",

&nbsp;  "applEmpNo":"ID-000002",

&nbsp;  "payCurrency":"CNY",

&nbsp;  "summary":"采购接口调用",

&nbsp;  "billtype":"ap_payapply_BT_S",

&nbsp;  "appseleAmount":19,

&nbsp;  "payAccountNo": "11234",

"vendorAccountNo": "11234",

"vendorAccountName": "11234",

&nbsp;  "paymentMode": "1",

&nbsp;  "claimLineDtos":\[

&nbsp;   {

&nbsp;    "vendorCode":"Sup-000001",

&nbsp;    "origCurAmount":19,

&nbsp;    "summary":"采购分录接口调用",

&nbsp;    "taxRateStr":"LzBUU",

&nbsp;    "paymenttype":"**********"

&nbsp;   }

&nbsp;  \]

&nbsp; }

&nbsp;\]

}

**返回参数示例:**

{

&nbsp;   "data": \[

&nbsp;       {

&nbsp;           "billNum": "FKSQ-********",

&nbsp;           "index": 0,

&nbsp;           "message": "自动生成审核成功",

&nbsp;           "sourceBillNum": "GF0004",

&nbsp;           "success": true

&nbsp;       }

&nbsp;   \],

&nbsp;   "errorCode": "0",

&nbsp;   "message": "具体付款单号是否成功看data里的数据",

&nbsp;   "status": true

}
