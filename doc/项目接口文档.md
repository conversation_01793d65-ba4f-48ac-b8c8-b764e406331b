## 项目保存提交审核接口

### 接口描述：

- 广交云.【项目】.审核后 **推送** 金蝶云·星空旗舰版.【项目】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/basedata/bd_project/saveProject

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/basedata/bd_project/saveProject

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|                       |     |        |                        |     |
|-----------------------| --- |--------|------------------------| --- |
| **参数名称**              | **参数类型** | **必填** | **说明**                 | **层级** |
| id                    | String | 否      | 修改时需传                  | 1   |
| number                | String | 是      | 项目编码                   | 1   |
| name                  | String | 是      | 项目名称                   | 1   |
| createorg_number      | String | 是      | 创建组织.编码                | 1   |
| gjwl_appid            | String | 是      | 外部编码                   | 1   |
| gjwl_sourcesystemtype | String | 是      | 来源系统，固定值，传"广交云供应链管理系统" | 1   |

**请求参数示例（沙箱环境）:**

{

"data":\[

{

"createorg_number":"gjwl",

"number":"Prj-000001",

"name":"测试项目01",

"gjwl_appid":"iIa9l"，

"gjwl_sourcesystemtype":"广交云供应链管理系统"

}

\]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data":{

"result":{

"saveResult":\[

{

"number":"prj-000008",

"keys":{

"number":"prj-000008"

},

"billStatus":true,

"billIndex":0,

"id":"2274786458764061696",

"type":"Add",

"errors":\[\]

}

\],

"finalStatus":true,

"finalErrorCode":"0",

"finalMessage":null,

"submitResult":\[

{

"number":"prj-000008",

"billStatus":true,

"id":"2274786458764061696",

"errors":\[\]

}

\],

"auditResult":\[

{

"number":"prj-000008",

"billStatus":true,

"id":"2274786458764061696",

"errors":\[\]

}

\]

}

},

"errorCode":"0",

"message":null,

"status":true

}

## 项目反审核接口

### 接口描述：

- 广交云.【项目】.审核后需修改保存前需 **反审核** 金蝶云·星空旗舰版.【项目】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/basedata/bd_project/batchUnaudit

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/basedata/bd_project/batchUnaudit

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **是否必填** | **参数类型** | **描述说明** |
| id  | "1402088445405483008","5764822583698205696" | 是   | Array&lt;Long&gt; | 项目Id |

**请求参数示例（沙箱环境）:**

{

"data":{

"id":\[

"1402088445405483008",

"5764822583698205696"

\]

}

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| filter | 操作执行的过滤条件，如：billno in ('CGSQ-220228-000258','CGSQ-220228-000259') | String | 2   | 过滤条件 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| totalCount | "3" | String | 2   | 总数  |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data":{

"failCount":"0",

"filter":"\[number IN ('Prj-000001')\]",

"result":\[

{

"billStatus":true,

"errors":\[\],

"id":"2058208592103736320",

"number":"Prj-000001"

}

\],

"successCount":"1",

"totalCount":"1"

},

"errorCode":"0",

"message":null,

"status":true

}
