
## 下推并保存接口

### 接口描述：

- 用于单据下推的场景：

1.  广交云.【销售出库单】.审核后需 **下推** 金蝶云·星空旗舰版.生成【销售出库单】（对应**6.1.1.销售订单下推销售出库单**）
2.  广交云.【销售退货单】.审核后需 **下推** 金蝶云·星空旗舰版.生成【销售退货单】（对应 **6.1.3.销售出库单下推销售退货单**）
3.  广交云.【采购入库单】.审核后需 **下推** 金蝶云·星空旗舰版.生成【采购入库单】（对应 **6.1.4.采购订单下推采购入库单**）
4.  广交云.【采退出库单】.审核后需 **下推** 金蝶云·星空旗舰版.生成【采购退料单】（对应 **6.1.5.采购入库单下推采购退料单**）
5.  广交云.【调拨单(调入)】.审核后需 **下推** 金蝶云·星空旗舰版.生成【分步式调入单】（对应 **6.1.6.分步调出单下推分步调入单**）

- **说明：调用该接口后单据为暂存状态，还需要调用对应的保存接口才会执行保存提交审核操作。如：调用 6.1.1.销售订单下推销售出库单 接口后，需要调用 5.2.销售出库退货单保存接口 接口才会执行保存提交审核操作，并且如果有部分出库的业务，也适用，参考 6.1.2.部分出库场景处理和说明。**

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/msbd/pushAndSave

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/msbd/pushAndSave

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **字段参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| sourceEntityNumber | String | 是   | 源单单据标识<br><br>(<br><br>如为销售订单下推销售出库单时：传sm_salorder；<br><br>如为销售出库单下推销售退货单时：传im_saloutbill；<br><br>如为采购订单下推采购入库单时：传pm_purorderbill；<br><br>如为采购入库单下推采购退料单时：传im_purinbill；<br><br>如为分步调出单下推分步调入单时：传im_transoutbill；<br><br>) | 1   | "hFu14" |
| targetEntityNumber | String | 是   | 目标单单据标识<br><br>(<br><br>如为销售订单下推销售出库单时：传im_saloutbill；<br><br>如为销售出库单下推销售退货时：传im_saloutbill；<br><br>如为采购订单下推采购入库单时：传im_purinbill；<br><br>如为采购入库单下推采购退料单时：传im_purinbill；<br><br>分步调出单下推分步调入单时：传im_transinbill；<br><br>) | 1   | "eCAHq" |
| ruleId | String | 是   | 转换规则ID<br><br>(<br><br>如为销售订单下推销售出库单时：传610056021305574400<br><br>；<br><br>如为销售出库单下推销售退货单时：传705622291240812544<br><br>；<br><br>如为采购订单下推采购入库单时：传565033700123759616；<br><br>如为采购入库单下推采购退料单时：传705591916980436992<br><br>；<br><br>如为分步调出单下推分步调入单时：传475190152356975616；<br><br>) | 1   | "YAiD3" |
| sourceBills | List&lt;SelectedBill&gt; | 是   | 源单单据信息，支持整单下推、按分录下推、合并下推 | 1   | \-  |
| &nbsp; billId | String | 是   | 源单ID | 2   | "o7qPe" |
| &nbsp; entryIds | List&lt;String&gt; | 否   | 源单分录ID集合，为空则整单下推 | 2   | "CweDV" |

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| status | false | boolean | 1   | 状态：true - 成功, false - 失败 |
| errorCode | "L48yi" | String | 1   | 错误代码 |
| message | "NN5Kh" | String | 1   | 错误信息 |
| data | \-  | PushApiResult | 1   | 下推结果 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| pushResult | "\[\]" | Array&lt;Map&gt; | 3   | 下推并保存返回结果详细信息 |
| &nbsp; pushSuccess | false | boolean | 4   | 下推是否成功 |
|     |     |     |     |     |
| &nbsp; pushFailMessage | "dvsNV" | String | 4   | 下推失败整体提示 |
| &nbsp; pushFailReports | \-  | List&lt;BillFailReport&gt; | 4   | 源单下推失败报告 |
| &nbsp;   billId | "JTq42" | String | 5   | 单据ID |
| &nbsp;   billNo | "ZJ3TK" | String | 5   | 单据编号 |
| &nbsp;   failMessage | "K7tEv" | String | 5   | 失败原因 |
| &nbsp;   rowFailMessages | \-  | List&lt;RowFailMessage&gt; | 5   | 分录失败详情 |
| &nbsp;       entryRowId | "gLJIZ" | String | 6   | 分录ID |
| &nbsp;       rowIndex | 178 | int | 6   | 分录索引 |
| &nbsp;       failMessage | "RQN3o" | String | 6   | 失败原因 |
| &nbsp; saveSuccess | false | boolean | 4   | 目标单保存是否成功 |
| &nbsp; saveFailMessage | "BLg6x" | String | 4   | 目标单保存失败整体提示 |
| &nbsp; saveFailReports | \-  | List&lt;BillFailReport&gt; | 4   | 目标单保存失败报告 |
| &nbsp;   billId | "NM48d" | String | 5   | 单据ID |
| &nbsp;   billNo | "2dGHy" | String | 5   | 单据编号 |
| &nbsp;   failMessage | "9XgXa" | String | 5   | 失败原因 |
| &nbsp;   rowFailMessages | \-  | List&lt;RowFailMessage&gt; | 5   | 分录失败详情 |
| &nbsp;       entryRowId | "ICisX" | String | 6   | 分录ID |
| &nbsp;       rowIndex | 205 | int | 6   | 分录索引 |
| &nbsp;       failMessage | "ZTrGW" | String | 6   | 失败原因 |
| &nbsp; targetBills | \-  | List&lt;TargetBill&gt; | 4   | 生成的目标单信息 |
| &nbsp;   billId | "JNAOH" | String | 4   | 目标单ID |
| &nbsp;   billNo | "5No6M" | String | 4   | 目标单单据编号 |
| &nbsp;   sourceBills | \-  | List&lt;SourceBill&gt; | 5   | 关联源单信息 |
| &nbsp;       srcBillId | "z5EEy" | String | 6   | 源单ID |
| &nbsp;       srcBillNo | "44jq1" | String | 6   | 源单单据编号 |
| &nbsp;       **targetResult** | **"\[\]"** | **Array&lt;Map&gt;** | **3** | **下推并保存返回目标单据详细信息** |
| &nbsp;       **fid** | **"2275406296180082688"** | **String** | **4** | **目标单据ID，需记录，用于修改单据** |
| &nbsp;       **fbillno** | **"XSCK-250806-000001"** | **String** | **4** | **目标单据编号** |
| &nbsp;       **fseq** | **"1"** | **String** | **4** | **序号** |
| &nbsp;       **fentryid** | **"2275406300315666432"** | **String** | **4** | **目标单据分录ID，需记录，用于修改单据** |
| &nbsp;                             **fmasterid** | **"2275406300315666432"** | **String** | **4** | **物料ID** |
| &nbsp;       **fnumber** | **"Item-000000100"** | **String** | **4** | **物料编码** |