API调用流程图
```mermaid
graph TD

    A[开始API调用] --> B[创建KdOpRequest实例]

    B --> C[设置请求参数 setParam]

    C --> D[设置日志客户端 sdkLogClient]

    D --> E[调用execute方法]

    E --> F{获取访问令牌}

    F -->|通过KdClinet| G[api.getAccessToken]

    F -->|直接传入| H[使用KdAccessTokenDTO]

    G --> I{令牌获取成功?}

    I -->|否| J[抛出KingDeeOpException]

    I -->|是| K[检查needCall标志]

    H --> K

    K --> L{需要调用金蝶?}

    L -->|否| M[返回null]

    L -->|是| N[参数验证]

    N --> O{参数验证通过?}

    O -->|否| P[抛出ServiceException]

    O -->|是| Q[构建日志记录器]

    Q --> T[构建HTTP请求]

    T --> U[设置请求URL:

    host + /kapi + urlPath]

    U --> V[设置请求头]

    V --> AA[序列化请求参数为JSON]

    AA --> BB[记录请求内容到日志]

    BB --> CC[发送HTTP POST请求]

    CC --> DD{HTTP请求成功?}

    DD -->|否| EE[根据状态码抛出异常]

    DD -->|是| FF[获取响应内容]

    FF --> MM[记录响应内容到日志]

    MM --> NN[反序列化响应为对象]

    NN --> VV[结束]

  

    %% 异常处理流程

    J --> VV3[保存SDK日志、结束]

    P --> VV4[保存SDK日志、结束]

    EE --> VV5[保存SDK日志、结束]

    M --> VV2[结束]

    %% 样式定义

    classDef startEnd fill:#e1f5fe

    classDef process fill:#f3e5f5

    classDef decision fill:#fff3e0

    classDef error fill:#ffebee

    classDef success fill:#e8f5e8

    class A,VV,VV2,VV3,VV4,VV5 startEnd

    class B,C,D,Q,R,S,T,U,V,W,X,Y,Z,AA,BB,CC,FF,MM,NN,UU process

    class F,I,K,L,O,DD,OO,RR decision

    class J,P,EE,GG,HH,II,JJ,KK,LL,PP,SS error

    class M,TT success
```
技术架构与数据流图

```mermaid
graph TB

  

  

    A[业务应用] --> B[创建具体Request对象]

  

    B --> C[设置业务参数]

  

  

    C --> D[KdOpRequest基类]

  

    D --> E[KingdeeDefaultOpClient单例]

  

    E --> F[requestContent方法]

  

  

    F --> G[获取AccessToken]

  

    G --> H{Token有效性检查}

  

    H -->|有效| I[提取服务器URL和认证信息]

  

    H -->|无效| J[抛出认证异常]

  

    I --> K[参数验证与序列化]

  

    K --> KL[构建日志记录器]

  

    KL --> L[构建HTTP请求]

  

    L --> M[设置请求头信息]

  

    M --> N[生成幂等性Key]

  

    N --> R[OkHttpClient]

  

    R --> S[发送POST请求到金蝶服务器]

  

    S --> T{HTTP响应状态}

  

    T -->|2xx成功| Y[获取响应体]

  

    T -->|4xx/5xx错误| Z[HTTP错误处理]

  

    Y --> AA[JSON反序列化]

  

    AA --> BB[业务响应码检查]

  

  

        Z --> VV[结束]

  

        BB --> JJ{业务处理成功?}

  

        JJ -->|失败| KK[业务异常处理]

  

        JJ -->|成功| LL[返回业务数据]

  

        KK --> PP[保存到日志存储]

  

        LL --> QQ[KdResponseWrapper包装]

  

        QQ --> PP

  

    %% 异常流向业务应用

  

    J --> VV1[结束]

  

    PP --> VV2[结束]

  

    %% 样式定义

  

    classDef client fill:#e3f2fd

  

    classDef core fill:#f3e5f5

  

    classDef auth fill:#fff3e0

  

    classDef process fill:#e8f5e8

  

    classDef log fill:#fce4ec

  

    classDef http fill:#f1f8e9

  

    classDef kingdee fill:#fff8e1

  

    classDef response fill:#e0f2f1

  

    classDef exception fill:#ffebee

  

    classDef storage fill:#f9fbe7

  

    classDef result fill:#e8eaf6

    classDef startEnd fill:#e1f5fe

  

    class A,B,C client

  

    class D,E,F core

  

    class G,H,I,J auth

  

    class K,KL,L,M,N process

  

    class O,P,Q log

  

    class R,S,T http

  

    class U,V,W,X kingdee

  

    class Y,AA,BB response

  

    class Z,CC,DD,EE,FF,GG,HH,II,JJ,KK exception

  

    class MM,NN,OO,PP storage

  

    class LL,QQ,RR result

  

    class VV startEnd
```
