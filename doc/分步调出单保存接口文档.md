
## 分步调出单保存接口

### 接口描述：

- 广交云.【调拨单(调出)】.审核后 **推送** 金蝶云·星空旗舰版.【分步调出单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/im_transoutbill/SaveTransOutBill

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/im_transoutbill/SaveTransOutBill](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| billno | String | 否   | 单据编号 | 1   | "DBCK-231201-000001" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "PYD-20250505-005"，传广交云单号 |
| &nbsp;      gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
| biztime | Date | 是   | 业务日期 | 1   | "2023-12-01" |
| comment | String | 否   | 备注  | 1   | "\*" |
| transtype | String | 是   | 调拨类型 | 1   | 固定值，传"A" |
| org_number | String | 是   | 调出组织.编码 | 1   | 固定值，传"1011" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | "im_AllotOutBill_STD_BT_S"，固定值 |
| biztype_number | String | 是   | 业务类型.编码（310-普通调拨） | 1   | 固定值，传"310" |
| invscheme_number | String | 是   | 库存事务.编码（315-分步式调出在途） | 1   | 固定值，传"315" |
| inorg_number | String | 是   | 调入组织.编码 | 1   | 固定值，传"1011" |
| settlescurrency_number | String | 否   | 本位币.货币代码 | 1   | 固定值，传"CNY" |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| &nbsp; id | Long | 否   | 物料明细.id | 2   | "2067883217927597056" |
| &nbsp; linetype_number | String | 是   | 行类型.编码(010-物资) | 2   | 固定值，传"010" |
| &nbsp; material_number | String | 是   | 物料编码.编码 | 2   | "item-0001"，广交产品编码 |
| &nbsp; unit_number | String | 否   | 库存单位.编码 | 2   | "kg" |
| &nbsp; qty | Decimal | 是   | 物料明细.数量 | 2   | "20" |
| &nbsp; warehouse_number | String | 是   | 调出仓库.编码 | 2   | "CK-001" |
| &nbsp; lotnumber | String | 否   | 物料明细.批号 | 2   | "P-0001"，广交批次，物料启用批号时必传 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |
| &nbsp; owner_number | String | 否   | 调入货主.编码 | 2   | 固定值，传"1011" |
| &nbsp; producedate | Date | 否   | 物料明细.生产日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| &nbsp; expirydate | Date | 否   | 物料明细.到期日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| &nbsp; outinvstatus_number | String | 是   | 调出库存状态.编码(110-可用) | 2   | 固定值，传"110" |
| &nbsp; outinvtype_number | String | 是   | 调出库存类型.编码(110-普通) | 2   | 固定值，传"110" |
| &nbsp; outownertype | String | 是   | 调出货主类型 bd_supplier:供应商, bd_customer:客户, bos_org:核算组织 | 2   | 固定值，传"bos_org" |
| &nbsp; outowner_number | String | 是   | 调出货主.编码 | 2   | 固定值，传"1011" |
| &nbsp; outkeepertype | String | 是   | 调出保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| &nbsp; outkeeper_number | String | 是   | 调出保管者.编码 | 2   | 固定值，传"1011" |
| &nbsp; isfreegift | Boolean | 否   | 物料明细.赠品 | 2   | false |
| &nbsp; entrycomment | String | 否   | 物料明细.备注 | 2   | ""  |
| &nbsp; inwarehouse_number | String | 否   | 调入仓库.编码 | 2   | "CK-002" |
| &nbsp; ownertype | String | 是   | 调入货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| &nbsp; keepertype | String | 是   | 调入保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| &nbsp; invstatus_number | String | 是   | 调入库存状态.编码(114-在途) | 2   | 固定值，传"110" |
| &nbsp; invtype_number | String | 是   | 调入库存类型.编码(110-普通) | 2   | 固定值，传"110" |
| project_number | String | 否   | 调出项目编码 | 2   | "P-0001"，待广交确认 |
| inproject_number | String | 否   | 调入项目编码 | 2   | "P-0001"，待广交确认 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data": \[

&nbsp;       {

&nbsp;           "billno": "DBCK-250822-000001",

&nbsp;           "biztime": "2025-08-22",

&nbsp;           "comment": "备注",

&nbsp;           "bookdate": "2025-08-22",

&nbsp;           "transtype": "A",

&nbsp;           "org_number": "1011",

&nbsp;           "billtype_number": "im_AllotOutBill_STD_BT_S",

&nbsp;           "biztype_number": "310",

&nbsp;           "invscheme_number": "315",

&nbsp;           "inorg_number": "1011",

&nbsp;           "settlescurrency_number": "CNY",

&nbsp;           "gjwl_thirdparty_billno": "DBCK-250822-000001",

&nbsp;           "gjwl_sourcesystemtype": "广交云供应链管理系统",

&nbsp;           "billentry": \[

&nbsp;               {

&nbsp;                   "linetype_number": "010",

&nbsp;                   "material_number": "Item-000000100",

&nbsp;                   "unit_number": "pcs",

&nbsp;                   "qty": "20",

&nbsp;                   "warehouse_number": "CK-001",

&nbsp;                   "lotnumber": "123",

&nbsp;                   "owner_number": "1011",

&nbsp;                   "producedate": "2025-07-29",

&nbsp;                   "expirydate": "2029-07-29",

&nbsp;                   "outinvstatus_number": "110",

&nbsp;                   "outinvtype_number": "110",

&nbsp;                   "outownertype": "bos_org",

&nbsp;                   "outowner_number": "1011",

&nbsp;                   "outkeepertype": "bos_org",

&nbsp;                   "outkeeper_number": "1011",

&nbsp;                   "project_number": "KD-P-ALL_SYS",

&nbsp;                   "inproject_number": "KD-P-ALL_SYS",

&nbsp;                   "isfreegift": false,

&nbsp;                   "entrycomment": "单据体备注",

&nbsp;                   "inwarehouse_number": "CK-005",

&nbsp;                   "ownertype": "bos_org",

&nbsp;                   "keepertype": "bos_org",

&nbsp;                   "keeper_number": "1011",

&nbsp;                   "invstatus_number": "114",

&nbsp;                   "invtype_number": "110",

&nbsp;                   "gjwl_product_lotno": "123"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data": {

"failCount": "0",

"result": \[

{

"billIndex": 0,

"billStatus": true,

"errors": \[\],

"id": "2271234785781237760",

"keys": {

"billno": "DBCK-250731-000001"

},

"number": "DBCK-250731-000001",

"type": "Add"

}

\],

"successCount": "1"

},

"errorCode": "0",

"message": null,

"status": true

}
