
## 分步调入单保存接口

### 接口描述：

- 广交云.【调拨单(调入)】.修改操作.审核后 **推送** 金蝶云·星空旗舰版.【分步调入单】
- 而广交云.【调拨单(调入)】.新增操作.需调用 **6.1.6.分步调出单下推分步调入单 下推** 生成金蝶云·星空旗舰版.【分步调入单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/im/im_transinbill/batchAdd_V2

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/im/im_transinbill/batchAdd_V2](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| org_number | String | 是   | 调入组织.编码 | 1   | "BU-001"，固定值 |
| billno | String | 否   | 单据编号 | 1   | "ZnjbA" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "PYD-20250505-005"，传广交云单号 |
| &nbsp;      gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
| biztime | Date | 是   | 业务日期 | 1   | "2023-12-13" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | "im_AllotInBill_STD_BT_S"，固定值 |
| biztype_number | String | 是   | 业务类型.编码 | 1   | "310"，固定值 |
| invscheme_number | String | 是   | 库存事务.编码 | 1   | "316" |
| transtype | String | 是   | 调拨类型 A:组织内调拨, B:跨组织调拨 | 1   | "A"，固定值 |
| transit | String | 是   | 在途归属 A:调出货主, B:调入货主 | 1   | "A"，固定值 |
| outorg_number | String | 是   | 调出组织.编码 | 1   | "BU-002" |
| settlescurrency_number | String | 是   | 本位币.货币代码 | 1   | "CNY" |
| comment | String | 否   | 备注  | 1   | "U4Y7j" |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| &nbsp; id | Long | 否   | 物料明细.id | 2   | "2067883217927597056" |
| &nbsp; linetype_number | String | 是   | 行类型.编码 | 2   | "010" |
| &nbsp; material_number | String | 是   | 物料编码.编码 | 2   | "item-001" |
| &nbsp; mversion_number | String | 否   | 物料版本.物料版本编码 | 2   | "V1" |
| &nbsp; unit_number | String | 是   | 库存单位.编码 | 2   | "pcs" |
| &nbsp; qty | Decimal | 是   | 物料明细.数量 | 2   | "10" |
| &nbsp; warehouse_number | String | 是   | 调入仓库.编码 | 2   | "CK-001" |
| &nbsp; invstatus_number | String | 是   | 调入库存状态.编码(110: 可用) | 2   | "110" |
| &nbsp; ownertype | String | 是   | 物料明细.调入货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org" |
| &nbsp; owner_number | String | 是   | 调入货主.编码 | 2   | "BU-001" |
| &nbsp; keepertype | String | 是   | 物料明细.调入保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org" |
| &nbsp; keeper_number | String | 是   | 调入保管者.编码 | 2   | "BU-001" |
| &nbsp; producedate | Date | 否   | 物料明细.生产日期 | 2   | "2023-12-25"，物料启用保质期时必传 |
| &nbsp; expirydate | Date | 否   | 物料明细.有效期至 | 2   | "2023-12-25"，物料启用保质期时必传 |
| &nbsp; invtype_number | String | 是   | 调入库存类型.编码(110： 普通) | 2   | "110" |
| &nbsp; outinvstatus_number | String | 是   | 调出库存状态.编码(114: 在途) | 2   | "114" |
| &nbsp; outinvtype_number | String | 是   | 调出库存类型.编码(110： 普通) | 2   | "110" |
| &nbsp; outownertype | String | 是   | 物料明细.调出货主类型 bos_org:业务组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org" |
| &nbsp; outkeepertype | String | 是   | 物料明细.调出保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | "bos_org" |
| &nbsp; outowner_number | String | 是   | 调出货主.编码 | 2   | "xbEa8" |
| &nbsp; outkeeper_number | String | 是   | 调出保管者.编码 | 2   | "6JRMK" |
| &nbsp; outwarehouse_number | String | 是   | 调出仓库.编码 | 2   | "R71K3" |

**请求参数示例（沙箱环境）:**

{

"data":\[

{

"billno":"test1119001",

"org_number":"gjwl",

"billtype_number":"im_OtherOutBill_STD_BT_S",

"biztype_number":"355",

"invscheme_number":"355",

"customer_number":"Cus-000006",

"biztime":"2024-11-11",

"billentry":\[

{

"linetype_number":"010",

"material_number":"Item-00000002",

"qty":"5",

"warehouse_number":"CK-002",

"outinvtype_number":"110",

"outinvstatus_number":"110",

"outownertype":"bos_org",

"outowner_number":"gjwl",

"outkeepertype":"bos_org",

"outkeeper_number":"gjwl",

}

\]

}

\]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data": {

"failCount": "0",

"result": \[

{

"billIndex": 0,

"billStatus": true,

"errors": \[\],

"id": "2271265513982133248",

"keys": {

"billno": "DBRK-250731-000001"

},

"number": "DBRK-250731-000001",

"type": "Add"

}

\],

"successCount": "1"

},

"errorCode": "0",

"message": null,

"status": true

}
