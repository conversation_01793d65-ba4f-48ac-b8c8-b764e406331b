## 其他出库单保存接口

### 接口描述：

- 广交云.【报损单(不合格报损)】.审核后 **推送** 金蝶云·星空旗舰版.【其他出库单】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/im/im_otheroutbill/saveOtherOutBill

**请求 URL（沙箱环境）:**

[https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/im/im_otheroutbill/saveOtherOutBill](https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/cas/recClaimInterface/recClaimInterface)

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |     |
| --- | --- | --- | --- | --- | --- |
| **参数名** | **参数类型** | **是否必填** | **描述说明** | **层级** | **参数值** |
| org_number | String | 是   | 库存组织.编码 | 1   | "BU-20109"，固定值，传"1011" |
| billno | String | 否   | 单据编号 | 1   | "5Xrbr" |
| gjwl_thirdparty_billno | String | 是   | 外部系统单号 | 1   | "PYD-20250505-005"，传广交云单号 |
| &nbsp;      gjwl_sourcesystemtype | String | 是   | 来源系统 | 1   | 固定值，传"广交云供应链管理系统" |
| biztime | Date | 是   | 业务日期 | 1   | "2022-07-11" |
| billtype_number | String | 是   | 单据类型.编码 | 1   | "im_OtherInBill_STD_BT_S"，固定值 |
| biztype_number | String | 是   | 业务类型.编码（355-其他出库、322-资产领料、3551-其他出库退回、356-VMI其他出库） | 1   | 固定值，传"355" |
| invscheme_number | String | 是   | 库存事务.编码（355-其他出库、3551-其他出库退、356-VMI其他出库） | 1   | 固定值，传"355" |
| bizdept_number | String | 是   | 领用部门.编码 | 1   | 具体传值待确认-固定值，先传"1011EY11EY1102<br><br>" |
| billentry | Entries | 是   | 物料明细 | 1   | {},{} |
| linetype_number | String | 是   | 行类型.编码(010-物资) | 2   | 固定值，传"010" |
| material_number | String | 是   | 物料编码.编码 | 2   | "item-001"，广交产品编码 |
| qty | Decimal | 是   | 物料明细.数量 | 2   | "470.97" |
| warehouse_number | String | 是   | 仓库.编码 | 2   | "CK001" |
| outinvtype_number | String | 是   | 出库库存类型.编码（110-普通、111-赠品、113-VMI） | 2   | 固定值，传"110" |
| outinvstatus_number | String | 是   | 出库库存状态.编码(110-可用) | 2   | 固定值，传"110" |
| outownertype | String | 是   | 物料明细.出库货主类型 bd_supplier:供应商, bd_customer:客户, bos_org:核算组织 | 2   | 固定值，传"bos_org" |
| outowner_number | String | 是   | 出库货主.编码 | 2   | 固定值，传"1011" |
| outkeepertype | String | 是   | 物料明细.出库保管者类型 bos_org:库存组织, bd_supplier:供应商, bd_customer:客户 | 2   | 固定值，传"bos_org" |
| outkeeper_number | String | 是   | 出库保管者.编码 | 2   | 固定值，传"1011" |
| &nbsp;project_number | String | 否   | 项目号.项目编码 | 2   | "P001" |
| lotnumber | String | 否   | 物料明细.批号 | 2   | "IHUsI"，对应广交云批次，物料启用批号时必传 |
| gjwl_product_lotno | String | 否   | 物料明细.生产批号 | 2   | "IHUsI"，对应广交云生产批号 |
| &nbsp; producedate | Date | 否   | 物料明细.生产日期 | 2   | "2022-07-07"，物料启用保质期时必传 |
| &nbsp; expirydate | Date | 否   | 物料明细.到期日期 | 2   | "2022-07-07"，物料启用保质期时必传 |

**请求参数示例（沙箱环境）:**

{

&nbsp;   "data":\[

&nbsp;       {

&nbsp;           "org_number":"1011",

&nbsp;           "billno":"QTCK-250822-000002",

&nbsp;           "billtype_number":"im_OtherOutBill_STD_BT_S",

&nbsp;           "biztype_number":"355",

&nbsp;           "invscheme_number":"355",

&nbsp;           "bizdept_number":"1011EY11EY1102",

&nbsp;           "biztime":"2025-08-22",

&nbsp;           "bookdate":"2025-08-22",

&nbsp;           "comment":"备注",

&nbsp;           "gjwl_thirdparty_billno":"QTCK-250822-000002",

&nbsp;           "gjwl_sourcesystemtype":"广交云供应链管理系统",

&nbsp;           "billentry":\[

&nbsp;               {

&nbsp;                   "linetype_number":"010",

&nbsp;                   "material_number":"Item-000000100",

&nbsp;                   "unit_number":"pcs",

&nbsp;                   "qty":"10",

&nbsp;                   "lotnumber":"123",

&nbsp;                   "warehouse_number":"CK-001",

&nbsp;                   "outinvtype_number":"110",

&nbsp;                   "outinvstatus_number":"110",

&nbsp;                   "outownertype":"bos_org",

&nbsp;                   "outowner_number":"1011",

&nbsp;                   "outkeepertype":"bos_org",

&nbsp;                   "outkeeper_number":"1011",

&nbsp;                   "producedate":"2025-07-29",

&nbsp;                   "expirydate":"2029-07-29",

&nbsp;                   "invtype_number":"110",

&nbsp;                   "invstatus_number":"110",

&nbsp;                   "ownertype":"bos_org",

&nbsp;                   "owner_number":"1011",

&nbsp;                   "keepertype":"bos_org",

&nbsp;                   "keeper_number":"1011",

&nbsp;                   "project_number":"KD-P-ALL_SYS",

&nbsp;                   "price":"310.63",

&nbsp;                   "entrycomment":"分录备注",

&nbsp;                   "gjwl_product_lotno":"123"

&nbsp;               }

&nbsp;           \]

&nbsp;       }

&nbsp;   \]

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data":{

"failCount":"0",

"result":\[

{

"billIndex":0,

"billStatus":true,

"errors":\[\],

"id":"2068295602566871040",

"keys":{

"billno":"test1024002"

},

"number":"test1024002",

"type":"Add"

}

\],

"successCount":"1"

},

"errorCode":"0",

"message":null,

"status":true

}
