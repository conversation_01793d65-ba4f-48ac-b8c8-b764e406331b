package com.polarbear.kd.api.salesorder;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 销售订单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【销售订单】.新增/修改操作.审核后 推送 金蝶云·星空旗舰版.【销售订单】
 */
public class SalesOrderSaveRequest extends KdOpRequest<List<SalesOrder>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/sm/sm_salorder/saveSalOrder";
    }

    @Override
    public String logModule() {
        return "kd.sm_salorder.saveSalOrder";
    }

    @Override
    public Class<SalesOrderSaveResponse> getResponseClass() {
        return SalesOrderSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<SalesOrder>> setLogKey(Config<List<SalesOrder>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(SalesOrder::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
