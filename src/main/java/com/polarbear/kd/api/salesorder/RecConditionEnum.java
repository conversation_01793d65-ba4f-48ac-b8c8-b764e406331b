package com.polarbear.kd.api.salesorder;

/**
 * 收款条件枚举类 基于注释： SKTJ-1001_SYS:货到收款 SKTJ-1002_SYS:预收30%，货到收款70% SKTJ-1003_SYS:预收30%，开票30天后收款70%
 * SKTJ-1004_SYS:开票30天后收款 SKTJ-1005_SYS:开票60天后收款 SKTJ-1006_SYS:开票90天后收款 SKTJ-1007_SYS:月结30天
 * SKTJ-1008_SYS:按项目计划收款
 */
public enum RecConditionEnum {
  SKTJ_1001_SYS("SKTJ-1001_SYS", "货到收款"),
  SKTJ_1002_SYS("SKTJ-1002_SYS", "预收30%，货到收款70%"),
  SKTJ_1003_SYS("SKTJ-1003_SYS", "预收30%，开票30天后收款70%"),
  SKTJ_1004_SYS("SKTJ-1004_SYS", "开票30天后收款"),
  SKTJ_1005_SYS("SKTJ-1005_SYS", "开票60天后收款"),
  SKTJ_1006_SYS("SKTJ-1006_SYS", "开票90天后收款"),
  SKTJ_1007_SYS("SKTJ-1007_SYS", "月结30天"),
  SKTJ_1008_SYS("SKTJ-1008_SYS", "按项目计划收款");

  private final String code;
  private final String description;

  /**
   * 构造函数
   *
   * @param code 收款条件编号
   * @param description 收款条件描述
   */
  RecConditionEnum(String code, String description) {
    this.code = code;
    this.description = description;
  }

  /**
   * 获取收款条件编号
   *
   * @return 编号
   */
  public String getCode() {
    return code;
  }

  /**
   * 获取收款条件描述
   *
   * @return 描述
   */
  public String getDescription() {
    return description;
  }

  /**
   * 根据编号获取对应的枚举实例
   *
   * @param code 收款条件编号
   * @return 对应的RecConditionEnum枚举实例，如果未找到则返回null
   */
  public static RecConditionEnum fromCode(String code) {
    for (RecConditionEnum condition : RecConditionEnum.values()) {
      if (condition.getCode().equals(code)) {
        return condition;
      }
    }
    return null;
  }

  /**
   * 根据描述获取对应的枚举实例（如果描述是唯一的） 注意：如果存在相同描述的条件，此方法可能会返回第一个匹配项。 建议主要通过code进行查找。
   *
   * @param description 收款条件描述
   * @return 对应的RecConditionEnum枚举实例，如果未找到则返回null
   */
  public static RecConditionEnum fromDescription(String description) {
    for (RecConditionEnum condition : RecConditionEnum.values()) {
      if (condition.getDescription().equals(description)) {
        return condition;
      }
    }
    return null;
  }

  @Override
  public String toString() {
    return "RecConditionEnum{"
        + "code='"
        + code
        + '\''
        + ", description='"
        + description
        + '\''
        + '}';
  }
}
