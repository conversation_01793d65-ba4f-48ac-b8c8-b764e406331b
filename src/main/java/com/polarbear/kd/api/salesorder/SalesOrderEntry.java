package com.polarbear.kd.api.salesorder;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售订单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class SalesOrderEntry {
    
    /**
     * 物料明细.id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料编码.编码 (传广交云产品编码)
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 销售单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 发货组织.编码 (固定值，传"1011")
     */
    @JsonProperty("e_stockorg_number")
    private String eStockorgNumber;
    
    /**
     * 结算组织.编码 (固定值，传"1011")
     */
    @JsonProperty("entrysettleorg_number")
    private String entrysettleorgNumber;
    
    /**
     * 物料明细.单价
     */
    @JsonProperty("price")
    private BigDecimal price;
    
    /**
     * 物料明细.含税单价
     */
    @JsonProperty("priceandtax")
    private BigDecimal priceandtax;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 税率.编码 (广交云做映射，传对应编码)
     */
    @JsonProperty("taxrateid_number")
    private String taxrateidNumber;
    
    /**
     * 物料明细.税额
     */
    @JsonProperty("taxamount")
    private BigDecimal taxamount;
    
    /**
     * 物料明细.折扣方式 A:折扣率(%), B:单位折扣额, NULL:无
     */
    @JsonProperty("discounttype")
    private String discounttype;
    
    /**
     * 物料明细.单位折扣(率) (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountrate")
    private BigDecimal discountrate;
    
    /**
     * 物料明细.折扣额 (如折扣方式为折扣率(%)或单位折扣额时需传)
     */
    @JsonProperty("discountamount")
    private BigDecimal discountamount;
    
    /**
     * 物料明细.备注
     */
    @JsonProperty("remark")
    private String remark;
    
    /**
     * 仓库.编码 (广交云和星空数据维护一致)
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 物料明细.价税合计
     */
    @JsonProperty("amountandtax")
    private BigDecimal amountandtax;
    
    /**
     * 物料明细.金额
     */
    @JsonProperty("amount")
    private BigDecimal amount;
    
    /**
     * 物料明细.赠品 (默认为false，金额为0传true)
     */
    @JsonProperty("ispresent")
    private Boolean ispresent;
    
    /**
     * 项目编码.项目编码
     */
    @JsonProperty("project_number")
    private String projectNumber;
    
    /**
     * 物料明细.批号 (对应广交云批次)
     */
    @JsonProperty("lotnumber")
    private String lotnumber;
    
    /**
     * 物料明细.生产批号 (对应广交云生产批号)
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
}
