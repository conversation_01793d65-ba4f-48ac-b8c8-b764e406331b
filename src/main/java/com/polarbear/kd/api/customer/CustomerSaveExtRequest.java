package com.polarbear.kd.api.customer;

import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

public class CustomerSaveExtRequest extends KdOpRequest<List<Customer>> {
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/basedata/bd_customer/saveCustomer";
    }

    @Override
    public String logModule() {
        return "kd.bd_customer.saveCustomerExt";
    }

    @Override
    public Class<CustomerSaveExtResponse> getResponseClass() {
        return CustomerSaveExtResponse.class;
    }
}
