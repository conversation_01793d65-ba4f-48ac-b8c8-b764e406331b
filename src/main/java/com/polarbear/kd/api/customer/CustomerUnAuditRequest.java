package com.polarbear.kd.api.customer;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.core.KdOpRequest;

public class CustomerUnAuditRequest extends KdOpRequest<CustomerUnAudit> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/basedata/bd_customer/unAuditCustomer";
    }

    @Override
    public String logModule() {
        return "kd.bd_customer.unAuditCustomer";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return CustomerUnAuditResponse.class;
    }
}
