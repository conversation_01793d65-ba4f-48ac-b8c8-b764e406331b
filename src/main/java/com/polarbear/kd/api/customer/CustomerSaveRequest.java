package com.polarbear.kd.api.customer;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

public class CustomerSaveRequest extends KdOpRequest<List<Customer>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/basedata/bd_customer/saveCustomer";
    }

    @Override
    public String logModule() {
        return "kd.bd_customer.saveCustomer";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return CustomerSaveResponse.class;
    }
}
