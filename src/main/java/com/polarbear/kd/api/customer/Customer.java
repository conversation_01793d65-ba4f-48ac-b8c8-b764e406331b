package com.polarbear.kd.api.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 客户信息
 * <AUTHOR>
 * @date 2024/10/11
 */
@Data
public class Customer {
    /**
     * 客户名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 简称
     */
    @JsonProperty("simplename")
    private String simpleName;

    /**
     * 详细地址
     */
    @JsonProperty("bizpartner_address")
    private String bizPartnerAddress;
    /**
     * 联系人
     */
    @JsonProperty("linkman")
    private String linkman;
    /**
     * 联系电话
     */
    @JsonProperty("bizpartner_phone")
    private String bizPartnerPhone;
    /**
     * 统一社会信用代码
     */
    @JsonProperty("societycreditcode")
    private String societyCreditCode;
    /**
     * 纳税人识别号
     */
    @JsonProperty("tx_register_no")
    private String txRegisterNo;
    /**
     * 法人代表
     */
    @JsonProperty("artificialperson")
    private String artificialPerson;
    /**
     * 创建组织.编码
     */
    @JsonProperty("createorg_number")
    private String createorgNumber;
    /**
     * 客户编码
     */
    @JsonProperty("number")
    private String number;

    /**
     * 第三方编码(外部编码)
     */
    @JsonProperty("gjwl_appid")
    private String gjwlAppid;
}
