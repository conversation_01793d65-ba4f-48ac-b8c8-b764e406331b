package com.polarbear.kd.api.payment;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

public class PayApplyAutoCreateRequest extends KdOpRequest<List<Claim>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/ap/payApply/payApplyAutoCreate";
    }

    @Override
    public String logModule() {
        return "kd.payApply.create";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return PayApplyAutoCreateResponse.class;
    }
}
