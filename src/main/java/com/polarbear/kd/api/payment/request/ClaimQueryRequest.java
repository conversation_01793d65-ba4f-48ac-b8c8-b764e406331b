package com.polarbear.kd.api.payment.request;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.api.SimpleResponse;
import com.polarbear.kd.api.payment.response.PayLineResultResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;


/**
 *  查询报账单状态请求对象
 * <AUTHOR>
 * @Date 2024/10/11 18:20
 */
public class ClaimQueryRequest extends KdOpRequest<ClaimQueryPara> {
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/billSatus/billstatusquery";
    }

    @Override
    public String logModule() {
        return "kd.cas.billSatus.billStatusQuery";
    }

    @Override
    public Class<PayLineResultResponse> getResponseClass() {
        return PayLineResultResponse.class;
    }

    @Override
    public KdOpRequest<ClaimQueryPara> setLogKey(Config<ClaimQueryPara> config) {
        config.setKey1(ClaimQueryPara::getClaimNos);
        return super.setLogKey(config);
    }
    @Override
    public boolean standard() {
        return Boolean.FALSE;
    }
}
