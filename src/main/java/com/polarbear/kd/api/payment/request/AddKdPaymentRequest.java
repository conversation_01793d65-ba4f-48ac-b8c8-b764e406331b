package com.polarbear.kd.api.payment.request;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.api.payment.response.AddKdPaymentResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2024/10/11 17:43
 */
public class AddKdPaymentRequest extends KdOpRequest<List<AddKdPaymentPara>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/cas_paybill/savePayBill";
    }

    @Override
    public String logModule() {
        return "kd.cas_paybill.addSave";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
            return AddKdPaymentResponse.class;
    }

    @Override
    public KdOpRequest<List<AddKdPaymentPara>> setLogKey(Config<List<AddKdPaymentPara>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(AddKdPaymentPara::getBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return super.setLogKey(config);
    }

}
