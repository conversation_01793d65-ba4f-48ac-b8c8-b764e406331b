package com.polarbear.kd.api.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

@Data
public class Claim {
    /**
     * 结算组织编码
     */
    private String orgNum;

    /**
     * 结算组织编码
     */
    private String applyOrgNum;

    /**
     * 结算组织编码
     */
    private String payorgNum;

    /**
     * 申请日期
     */
    private String applyDate;

    /**
     * 数据来源系统
     */
    private String sourceSystem;
    /**
     * 来源系统单号
     */
    private String sourceNo;
    /**
     * 表单模板
     */
    private String tpCode;
    /**
     * 申请人工号
     */
    @NotBlank(message = "申请人工号不能为空")
    private String applEmpNo;
    /**
     * 业务大类编码
     */
    private String categoryCode;
    /**
     * 业务大类名称
     */
    private String categoryName;
    /**
     * 币种
     */
    private String payCurrency;
    /**
     * 付款类型
     */
    private String paymentMode;
    /**
     * 客商编码
     */
    private String vendorCode;
    /**
     * 客商名称
     */
    private String vendorName;
    /**
     * 客商银行账号
     */
    private String vendorAccountNo;
    /**
     * 客商银行账号名称
     */
    private String vendorAccountName;
    /**
     * 备注，报账事由
     */
    private String summary;
    /**
     * 付款事由
     */
    private String payPurpose;
    /**
     * 付款账号
     */
    private String payAccountNo;

    /**
     * 申请金额
     */
    private BigDecimal appseleAmount;

    /**
     * 单据类型
     */
    @JsonProperty("billtype")
    private String billType;

    /**
     * 付款状态
     */
    private String payStatus;

    /**
     * 数据行
     */
    private List<ClaimLineDto> claimLineDtos;
}
