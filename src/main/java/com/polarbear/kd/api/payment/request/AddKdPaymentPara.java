package com.polarbear.kd.api.payment.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *  金蝶付款单对象
 * <AUTHOR>
 * @Date 2024/10/9 16:43
 */

@Data
public class AddKdPaymentPara {

    /**
     * 单据编号
     */
    private String billno;

    /**
     * 业务日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date bizdate;

    /**
     * 分录
     */
    private List<AddKdPaymentDetail> entry;

    /**
     * 收款单位类型
     */
    private String payeetype;

    /**
     * 收款单位类型
     */
    private String description;

//    /**
//     * 多分录
//     */
//    private Boolean issingle;
    /**
     * 付款金额折本位币
     */
    private BigDecimal localamt;
    /**
     * 付款汇率
     */
    private BigDecimal exchangerate;
    /**
     * 付款金额
     */
    private BigDecimal actpayamt;
    /**
     * 结算号
     */
    private String settletnumber;
//    /**
//     * 转账附言
//     */
//    private String usage;
    /**
     * 收款账户名称
     */
    private String recaccbankname;
//
//    /**
//     * 收款行行号
//     */
//    private String recbanknumber;
//
    /**
     * 收款单位名称
     */
    private String payeename;
//
//    /**
//     * 收款单位基础资料标识
//     */
//    private String payeeformid;
//
//    /**
//     * 收款单位ID
//     */
//    private Long payee;
//
    /**
     * 收款账号
     */
    private String payeebanknum;
//
//    /**
//     * 收款账户ID
//     */
//    private Long payeeacctbank;
//
//    /**
//     * 收款账户基础资料标识
//     */
//    private String payeeaccformid;
//
    /**
     * 收款银行名称
     */
    private String payeebankname;
//
//    /**
//     * 付款方式：[CASH：现购，CREDIT：赊购]
//     */
//    private String paymentmode;
//
//    /**
//     * 期望付款日期
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date expectdate;
//
//    /**
//     * 汇率日期
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date exratedate;
//
    /**
     * 支付渠道：[bei：银企互联，notbei：非银企互联]
     */
    private String paymentchannel;
//
//    /**
//     * 补充合同号
//     */
//    private Boolean issupplecontract;
//
    /**
     * 收款单位类型
     */
    private String itempayeetype;
//
//    /**
//     * 对私支付
//     */
//    private Boolean ispersonpay;
//
//    /**
//     * 手续费
//     */
//    private BigDecimal fee;
//
//    /**
//     * 手续费承担方：[01：付款方，02：收款方，03：共同承担]
//     */
//    private String feepayer;
//
//    /**
//     * 收款账号币别
//     */
//    private Long payeecurrency;
//
//    /**
//     * 影像编号
//     */
//    private String imageno;
//
//    /**
//     * 业务类型
//     */
//    private String businesstype;
//
//    /**
//     * 收款现金账户ID
//     */
//    private Long payeeacctcash;
//
//    /**
//     * 付款单位编码
//     */
//    private String payernumber;
//
//    /**
//     * 付款单位组织机构代码(统一社会信用编码)
//     */
//    private String uniformsocialcreditcode;
//
    /**
     * 收款单位编码
     */
    private String payeenumber;
//
//    /**
//     * 实际交易日期
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date acttradedate;
//
//    /**
//     * 付款汇率换算方式：[0：直接汇率:1：间接汇率]
//     */
//    private String payquotation;
//
//    /**
//     * 手续费独立流水
//     */
//    private Boolean singlestream;
//
//    /**
//     * 收款方省
//     */
//    private String recprovince;
//
//    /**
//     * 收款方市县
//     */
//    private String reccity;
//
//    /**
//     * 收款方邮箱
//     */
//    private String recemail;
//
//    /**
//     * 收款行地址
//     */
//    private String recbankaddress;
//
//    /**
//     * 清算要求参数
//     */
//    private String auditparam;
//
//    /**
//     * 付款方式：[TRA，TRF，CHK]
//     */
//    private String paymethod;
//
//    /**
//     * 服务级别：[URGP：紧急支付，SDVA：当日支付，PRPT：优先支付，NURG：其他]
//     */
//    private String serlevel;
//
//    /**
//     * 支票类型：[BCHQ，CCHQ，CCCH，DRFT，ELDR]
//     */
//    private String checktype;
//
//    /**
//     * 寄送方式
//     */
//    private String sendway;
//
//    /**
//     * 支票用途
//     */
//    private String checkuse;
//
//    /**
//     * 收款行Swift Code
//     */
//    private String recswiftcode;
//
//    /**
//     * 收款行Routing Number
//     */
//    private String recroutingnum;
//
//    /**
//     * 收款行其他行号
//     */
//    private String recothercode;
//
//    /**
//     * 电文指示：[1：单电文，2：双电文]
//     */
//    private String instructmsg;
//
//    /**
//     * 收款方地址
//     */
//    private String recaddress;
//
//    /**
//     * 清算方式
//     */
//    private String settlementmethod;
//
//    /**
//     * 收款单位电话
//     */
//    private String mobile;
//
//    /**
//     * 收款方式：[0：收款账号，1：收款人FPS账号，2：收款人电话，3：收款方邮箱]
//     */
//    private String paymentterm;
//
//    /**
//     * 收款单位FPS账号
//     */
//    private String paymentfps;
//
//    /**
//     * 收款单位地区码
//     */
//    private String paymentareacode;
//
//    /**
//     * 通知收款单位
//     */
//    private Boolean inforpayment;
//
//    /**
//     * 通知收款单位邮箱
//     */
//    private String informrecemail;
//
//    /**
//     * 付款币别.id
//     */
//    private Long currency_id;
//
    /**
     * 付款币别.货币代码
     */
    private String currency_number;
//
//    /**
//     * 付款组织.id
//     */
//    private Long org_id;
//
    /**
     * 付款组织.编码
     */
    private String org_number;
//
//    /**
//     * 付款账号.id
//     */
//    private Long payeracctbank_id;
//
    /**
     * 付款账号.编码
     */
    private String payeracctbank_number;
//
//    /**
//     * 付款银行.id
//     */
//    private Long payerbank_id;
//
    /**
     * 付款银行.编码
     */
    private String payerbank_number;
//
//    /**
//     * 收款账户开户行.id
//     */
//    private Long payeebank_id;
//
//    /**
//     * 收款账户开户行.编码
//     */
//    private String payeebank_number;
//
//    /**
//     * 采购组织.id
//     */
//    private Long purorg_id;
//
//    /**
//     * 采购组织.编码
//     */
//    private String purorg_number;
//
//    /**
//     * 采购部门.id
//     */
//    private Long purdepartment_id;
//
//    /**
//     * 采购部门.编码
//     */
//    private String purdepartment_number;
//
//    /**
//     * 采购组.id
//     */
//    private Long purdept_id;
//
//    /**
//     * 采购组.编码
//     */
//    private String purdept_number;
//
//    /**
//     * 采购员.id
//     */
//    private Long purchaser_id;
//
//    /**
//     * 采购员.业务员编码
//     */
//    private String purchaser_operatornumber;
//
//    /**
//     * 销售组织.id
//     */
//    private Long salesorg_id;
//
//    /**
//     * 销售组织.编码
//     */
//    private String salesorg_number;
//
//    /**
//     * 销售部门.id
//     */
//    private Long salesdept_id;
//
//    /**
//     * 销售部门.编码
//     */
//    private String salesdept_number;
//
//    /**
//     * 销售组.id
//     */
//    private Long salesgroup_id;
//
//    /**
//     * 销售组.编码
//     */
//    private String salesgroup_number;
//
//    /**
//     * 销售员.id
//     */
//    private Long salesman_id;
//
//    /**
//     * 销售员.业务员编码
//     */
//    private String salesman_operatornumber;
//
//    /**
//     * 付款明细.结算汇率
//     */
//    private BigDecimal e_settlerate;
//
//
//    /**
//     * 结算号
//     */
//    private List<Long> draftbill;
//
//    /**
//     * 结算方式.id
//     */
//    private Long settletype_id;
//
    /**
     * 结算方式.编码
     */
    private String settletype_number;
//
//    /**
//     * 出纳.id
//     */
//    private Long cashier_id;
//
//    /**
//     * 本位币.id
//     */
//    private Long basecurrency_id;
//
    /**
     * 本位币.货币代码
     */
    private String basecurrency_number;
//
//    /**
//     * 单据类型.id
//     */
//    private Long billtype_id;
//
    /**
     * 单据类型.编码
     */
    private String billtype_number;
//
//    /**
//     * 付款账号.id
//     */
//    private Long payeracctcash_id;
//
    /**
     * 付款账号.账户编码
     */
    private String payeracctcash_number;
//
//    /**
//     * 内部账号.id
//     */
//    private Long inneraccount_id;
//
//    /**
//     * 委托付款组织.id
//     */
//    private Long entrustorg_id;
//
//    /**
//     * 申请付款组织.id
//     */
//    private Long applyorg_id;
//
//    /**
//     * 汇率表.id
//     */
//    private Long exratetable_id;
//
//    /**
//     * 汇率表.编码
//     */
//    private String exratetable_number;
//
//    /**
//     * 收款单位.id
//     */
//    private Long itempayee_id;
//
    /**
     * 收款单位.编码
     */
    private String itempayee_number;
//
//    /**
//     * 手续费账户.id
//     */
//    private Long feeactbank_id;
//
//    /**
//     * 手续费账户.编码
//     */
//    private String feeactbank_number;
//
//    /**
//     * 手续费币别.id
//     */
//    private Long feecurrency_id;
//
//    /**
//     * 手续费币别.货币代码
//     */
//    private String feecurrency_number;
//
//    /**
//     * 付款方国家或地区.id
//     */
//    private Long paycountry_id;
//
//    /**
//     * 支付类型.id
//     */
//    private Long paymentidentify_id;
//
//    /**
//     * 收款方国家或地区.id
//     */
//    private Long reccountry_id;
//
//
//    /**
//     * 交易类型.id
//     */
//    private Long crosstrantype_id;
//
//    /**
//     * 交易种类.id
//     */
//    private Long transtype_id;
//
//    /**
//     * 付款代理行.id
//     */
//    private Long payproxybank_id;
//
//    /**
//     * 付款用途（废弃）.id
//     */
//    private Long paymenttype_id;
//
//    /**
//     * 付款用途（废弃）.编码
//     */
//    private String paymenttype_number;

    /**
     * 银行账号编码
     */
    private String payeracctbank_bankaccountnumber;

    /**
     * 来源异构系统
     */
    private String gjwl_srcsystem;


    public AddKdPaymentPara() {
        this.gjwl_srcsystem = "A";
    }
}
