package com.polarbear.kd.api.payment.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/10/9 17:42
 */
@Data
public class AddKdPaymentDetail {

//    /**
//     * 分录.id
//     */
//    private Long id;
//
//    /**
//     * 分录.分录行号
//     */
//    private Integer seq;
//
//    /**
//     * 分录.备注
//     */
//    private String e_remark;

    /**
     * 分录.实付金额
     */
    private BigDecimal e_actamt;

//    /**
//     * 分录.实付折本币
//     */
//    private BigDecimal e_localamt;
//
    /**
     * 分录.应付金额
     */
    private BigDecimal e_payableamt;
//
//    /**
//     * 分录.应付折本币
//     */
//    private BigDecimal e_payablelocamt;
//
//    /**
//     * 分录.现金折扣
//     */
//    private BigDecimal e_discountamt;
//
//    /**
//     * 分录.折扣折本币
//     */
//    private BigDecimal e_discountlocamt;
//
//    /**
//     * 分录.核心单据类型
//     */
//    private String e_corebilltype;
//
//    /**
//     * 分录.核心单据号
//     */
//    private String e_corebillno;
//
//    /**
//     * 分录.核心单据号
//     */
//    private Integer e_corebillentryseq;
//
//    /**
//     * 分录.已锁定金额
//     */
//    private BigDecimal e_lockamt;
//
//    /**
//     * 分录.未锁定金额
//     */
//    private BigDecimal e_unlockamt;
//
//    /**
//     * 分录.已结算金额
//     */
//    private BigDecimal e_settledamt;
//
//    /**
//     * 分录.未结算金额
//     */
//    private BigDecimal e_unsettledamt;
//
//    /**
//     * 分录.税率（%）
//     */
//    private BigDecimal taxrate;
//
//    /**
//     * 分录.税额
//     */
//    private BigDecimal taxamt;
//
//    /**
//     * 分录.合同号
//     */
//    private String contractnumber;
//
//    /**
//     * 分录.退款金额
//     */
//    private BigDecimal e_refundamt;
//
//    /**
//     * 分录.退款说明
//     */
//    private String e_refunddes;
//
//    /**
//     * 分录.业务日期
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date entrybizdate;
//
//    /**
//     * 分录.已结算金额折本币
//     */
//    private BigDecimal e_settledlocalamt;
//
//    /**
//     * 分录.未结算金额折本位币
//     */
//    private BigDecimal e_unsettledlocalamt;
//
//    /**
//     * 付款用途.id
//     */
//    private Long e_paymenttype_id;
//
    /**
     * 付款用途.编码
     */
    private String e_paymenttype_number;
//
//    /**
//     * 合同实体.id
//     */
//    private String conbillentity_id;
//
//    /**
//     * 合同实体.编码
//     */
//    private String conbillentity_number;
//
    /**
     * 付款明细.往来单位类型
     */
    private String e_contactunittype;
//
//    /**
//     * 结算组织.id
//     */
//    private Long settleorg_id;
//
    /**
     * 结算组织.编码
     */
    private String settleorg_number;
//
//    /**
//     * 往来单位.id
//     */
//    private Long e_contactunit_id;
//
//
    /**
     * 往来单位.编码
     */
    private String e_contactunit_number;
//
//    /**
//     * 结算币别.id
//     */
//    private Long e_settlecur_id;
//
//
    /**
     * 结算币别.货币代码
     */
    private String e_settlecur_number;
//
//    /**
//     * 资金用途.id
//     */
//    private Long e_fundflowitem_id;
//
//    /**
//     * 资金用途.编码
//     */
//    private String e_fundflowitem_number;
//
//    /**
//     * 物料.id
//     */
//    private Long e_material_id;
//
//    /**
//     * 物料.编码
//     */
//    private String e_material_number;
//
//    /**
//     * 费用项目.id
//     */
//    private Long e_expenseitem_id;
//
//    /**
//     * 费用项目.编码
//     */
//    private String e_expenseitem_number;
//
//    /**
//     * 费用承担部门.id
//     */
//    private Long e_department_id;
//
//    /**
//     * 费用承担部门.编码
//     */
//    private String e_department_number;
//
//    /**
//     * 项目.id
//     */
//    private Long project_id;
//
//    /**
//     * 项目.项目编码
//     */
//    private String project_number;
//
//    /**
//     * 采购组织.id
//     */
//    private Long purorg_id;
//
    /**
     * 采购组织.编码
     */
    private String purorg_number;
//
//    /**
//     * 采购部门.id
//     */
//    private Long purdepartment_id;
//
    /**
     * 采购部门.编码
     */
    private String purdepartment_number;
//
//    /**
//     * 采购组.id
//     */
//    private Long purdept_id;
//
//    /**
//     * 采购组.编码
//     */
//    private String purdept_number;
//
//    /**
//     * 采购员.id
//     */
//    private Long purchaser_id;
//
//    /**
//     * 采购员.业务员编码
//     */
//    private String purchaser_operatornumber;
//
//    /**
//     * 销售组织.id
//     */
//    private Long salesorg_id;
//
//    /**
//     * 销售组织.编码
//     */
//    private String salesorg_number;
//
//    /**
//     * 销售部门.id
//     */
//    private Long salesdept_id;
//
    /**
     * 销售部门.编码
     */
    private String salesdept_number;
//
//    /**
//     * 销售组.id
//     */
//    private Long salesgroup_id;
//
//    /**
//     * 销售组.编码
//     */
//    private String salesgroup_number;
//
//    /**
//     * 销售员.id
//     */
//    private Long salesman_id;
//
//    /**
//     * 销售员.业务员编码
//     */
//    private String salesman_operatornumber;
//
    /**
     * 付款明细.结算汇率
     */
    private BigDecimal e_settlerate;

    /**
     * 往来项目
     */
    private String gjwl_project_number;

    /**
     * 应收金额
     */
    private BigDecimal e_receivableamt;

    /**
     * 结算组织
     */
    private String e_settleorg_number;

}
