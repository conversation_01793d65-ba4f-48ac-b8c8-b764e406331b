package com.polarbear.kd.api.payment;

import com.polarbear.kd.api.EmptyResponse;
import com.polarbear.kd.core.KdOpRequest;

public class PayApplyQueryRequest extends KdOpRequest<PayApplyQueryParam> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/ap/payApply/payApplyQuery";
    }

    @Override
    public String logModule() {
        return "kd.payApply.query";
    }

    @Override
    public Class<? extends EmptyResponse> getResponseClass() {
        return PayApplyQueryResponse.class;
    }
}
