package com.polarbear.kd.api.payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 单据行
 * <AUTHOR>
 * @date 2024/10/11
 */
@Data
public class ClaimLineDto {
    /**
     * 业务小类编码
     */
    private String subcategoryCode;

    /**
     * 业务小类名称
     */
    private String subcategoryName;

    /**
     * 行备注
     */
    private String summary;

    /**
     * 原币币种
     */
    private String origCurrency;

    /**
     * 原币金额
     */
    private BigDecimal origCurAmount;

    /**
     * 原币不含税金额
     */
    private BigDecimal origPrice;

    /**
     * 原币税额
     */
    private BigDecimal origTaxAmount;

    /**
     * 税率
     */
    private String taxRateStr;

    /**
     * 客商编码
     */
    private String vendorCode;

    /**
     * 支付类型
     */
    @JsonProperty("paymenttype")
    private String paymentType;
}
