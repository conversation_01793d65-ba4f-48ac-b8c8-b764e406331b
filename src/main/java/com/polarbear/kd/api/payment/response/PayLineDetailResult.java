package com.polarbear.kd.api.payment.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/10/17 13:56
 */
@Data
public class PayLineDetailResult {

    private String claimNo;

    private String invoiceClaimNo;

    /**
     * 行号
     */
    private Long lineNumber;

    /*
     * 付款状态
     */
    private String status;

    /*
     * 付款方式
     */
    private String paymentMethod;

    /*
     * 付款方式
     */
    private String payOuCode;

    /*
     * 银行账号名称
     */
    private String payAccountName;

    /*
     * 银行账号
     */
    private String payAccountNo;

    /*
     * 收款开户行
     */
    private String receiVendorBankName;

    /*
     * 付款金额
     */
    private BigDecimal actualPayAmount;

    /*
     * 币种
     */
    private String currency;

    /*
     * 供应商
     */
    private String vendorCode;

    /*
     * 收款方户名
     */
    private String vendorAccountName;

    /*
     * 收款账户
     */
    private String vendorAccountNo;

    /*
     * 支付时间 yyyy-mm-dd hh:mm:ss
     */
    private String payTime;

    /*
     * 结算号
     */
    private String settlementNum;

}
