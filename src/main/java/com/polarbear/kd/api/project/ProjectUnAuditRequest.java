package com.polarbear.kd.api.project;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

/**
 * 项目反审核接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【项目】.审核后需修改保存前需 反审核 金蝶云·星空旗舰版.【项目】
 */
public class ProjectUnAuditRequest extends KdOpRequest<ProjectUnAudit> {

    @Override
    public String getUrlPath() {
        return "/v2/basedata/bd_project/batchUnaudit";
    }

    @Override
    public String logModule() {
        return "kd.bd_project.unAuditProject";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return ProjectUnAuditResponse.class;
    }

    @Override
    public KdOpRequest<ProjectUnAudit> setLogKey(Config<ProjectUnAudit> config) {
        config.setKey1(projectUnAudit -> {
            if (projectUnAudit != null && projectUnAudit.getId() != null) {
                String keys = String.join(",", projectUnAudit.getId());
                return keys.substring(0, Math.min(keys.length(), 250));
            }
            return "";
        });
        return this;
    }
}
