package com.polarbear.kd.api.project;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 项目保存提交审核接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【项目】.审核后 推送 金蝶云·星空旗舰版.【项目】
 */
public class ProjectSaveRequest extends KdOpRequest<List<Project>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/basedata/bd_project/saveProject";
    }

    @Override
    public String logModule() {
        return "kd.bd_project.saveProject";
    }

    @Override
    public Class<ProjectSaveResponse> getResponseClass() {
        return ProjectSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<Project>> setLogKey(Config<List<Project>> config) {
        config.setKey1(projects -> {
            if (projects != null && !projects.isEmpty()) {
                String keys = String.join(",", projects.stream()
                    .map(Project::getNumber)
                    .toArray(String[]::new));
                return keys.substring(0, Math.min(keys.length(), 250));
            }
            return "";
        });
        return this;
    }
}
