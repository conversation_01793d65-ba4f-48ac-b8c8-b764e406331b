package com.polarbear.kd.api.project;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 项目保存响应数据
 *
 * <AUTHOR>
 */
@Data
public class ProjectSaveResponseData {

  /** 成功数量 */
  @JsonProperty("successCount")
  private String successCount;

  /** 失败数量 */
  @JsonProperty("failCount")
  private String failCount;

  /** 保存返回结果详细信息 */
  @JsonProperty("result")
  private List<SaveResultItem> result;

  /** 保存结果项 */
  @Data
  public static class SaveResultItem {

    /** 项目编码 */
    @JsonProperty("number")
    private String number;

    /** 主键信息 */
    @JsonProperty("keys")
    private Keys keys;

    /** 操作是否成功 */
    @JsonProperty("billStatus")
    private Boolean billStatus;

    /** 单据索引 */
    @JsonProperty("billIndex")
    private Integer billIndex;

    /** 项目ID */
    @JsonProperty("id")
    private String id;

    /** 操作类型 */
    @JsonProperty("type")
    private String type;

    /** 错误信息数据 */
    @JsonProperty("errors")
    private List<Object> errors;
  }

  /** 主键信息 */
  @Data
  public static class Keys {

    /** 项目编码 */
    @JsonProperty("number")
    private String number;
  }
}
