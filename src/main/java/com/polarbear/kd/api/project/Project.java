package com.polarbear.kd.api.project;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 项目信息
 * <AUTHOR>
 * @date 2024/12/13
 */
@Data
public class Project {
    /**
     * 项目ID（修改时需传）
     */
    @JsonProperty("id")
    private String id;

    /**
     * 项目编码
     */
    @JsonProperty("number")
    private String number;

    /**
     * 项目名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 创建组织.编码
     */
    @JsonProperty("createorg_number")
    private String createorgNumber;

    /**
     * 外部编码
     */
    @JsonProperty("gjwl_appid")
    private String gjwlAppid;

    /**
     * 来源系统，固定值，传"广交云供应链管理系统"
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;
}
