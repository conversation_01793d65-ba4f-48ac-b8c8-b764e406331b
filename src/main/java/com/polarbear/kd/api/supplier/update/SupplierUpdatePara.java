package com.polarbear.kd.api.supplier.update;


import com.polarbear.kd.api.supplier.para.BankInfo;
import com.polarbear.kd.api.supplier.para.Linkman;
import com.polarbear.kd.api.supplier.para.Supplier;
import com.polarbear.kd.api.supplier.para.TaxQualification;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.List;

/**
 * 供应商信息类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierUpdatePara extends Supplier {
    /**
     * 联系人分录
     */
    @Valid
    private List<Linkman> entryLinkman;

    /**
     * 银行信息分录
     */
    @Valid
    private List<BankInfo> entryBank;

    /**
     * 税务资质
     */
    @Valid
    private List<TaxQualification> entryTax;
}



