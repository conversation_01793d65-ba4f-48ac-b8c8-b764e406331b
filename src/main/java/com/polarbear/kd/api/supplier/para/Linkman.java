package com.polarbear.kd.api.supplier.para;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 联系人类
 */
@Data
public class Linkman {
    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     */
    @JsonProperty("contactperson")
    private String contactPerson;

    /**
     * 联系电话
     */
    @JsonProperty("phone")
    private String phone;

    /**
     * 是否默认联系人
     */
    @JsonProperty("isdefault_linkman")
    private boolean isDefaultLinkman;
}
