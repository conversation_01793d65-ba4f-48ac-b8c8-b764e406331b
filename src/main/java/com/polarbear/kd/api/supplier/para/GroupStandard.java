package com.polarbear.kd.api.supplier.para;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 分类标准类
 */
@Data
public class GroupStandard {
    /**
     * ID
     */
    private Long id;

    /**
     * 分类标准编码
     */
    @NotBlank(message = "分类标准编码不能为空")
    @JsonProperty("standardid_number")
    private String standardIdNumber;

    /**
     * 分类编码
     */
    @NotBlank(message = "分类编码不能为空")
    @JsonProperty("groupid_number")
    private String groupIdNumber;
}