package com.polarbear.kd.api.supplier.create;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.polarbear.kd.api.supplier.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 供应商信息类
 */
@Data
public class SupplierSavePara {
    /**
     * 编码
     */
    @NotBlank(message = "编码不能为空")
    @JsonProperty("number")
    private String number;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @JsonProperty("name")
    private String name;

    /**
     * 创建组织编码
     */
    @NotBlank(message = "创建组织编码不能为空")
    @JsonProperty("createorg_number")
    private String createOrgNumber;

    /**
     * 创建时间
     */
    @JsonProperty("createtime")
    private LocalDateTime createTime;

    /**
     * 简称
     */
    @JsonProperty("simplename")
    private String simpleName;

    /**
     * 统一社会信用代码
     */
    @JsonProperty("societycreditcode")
    private String societyCreditCode;

    /**
     * 伙伴类型
     */
    @JsonProperty("type")
    private String type;

    /**
     * 纳税人识别号
     */
    @JsonProperty("tx_register_no")
    private String txRegisterNo;

    /**
     * 法人代表
     */
    @JsonProperty("artificialperson")
    private String artificialPerson;


    /**
     * 联系人
     */
    @JsonProperty("linkman")
    private String linkman;

    /**
     * 联系电话
     */
    @JsonProperty("bizpartner_phone")
    private String bizPartnerPhone;

    /**
     * 详细地址
     */
    @JsonProperty("bizpartner_address")
    private String bizPartnerAddress;

    /**
     * 第三方编码
     */
    @JsonProperty("gjwl_appid")
    private String gjwlAppid;

    /**
     * 分类编码;01:批发企业;02:生产企业;waitgroup:待分类
     */
    @JsonProperty("supplyType")
    private String supplyType;
}



