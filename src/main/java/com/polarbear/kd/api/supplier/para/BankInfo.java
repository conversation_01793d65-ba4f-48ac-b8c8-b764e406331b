package com.polarbear.kd.api.supplier.para;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 银行信息类
 */
@Data
public class BankInfo {
    /**
     * ID
     */
    private Long id;

    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号不能为空")
    @JsonProperty("bankaccount")
    private String bankAccount;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空")
    @JsonProperty("accountname")
    private String accountName;

    /**
     * 是否默认银行
     */
    @JsonProperty("isdefault_bank")
    private boolean isdefaultBank;

    /**
     * 开户银行编码
     */
    @NotBlank(message = "开户银行编码不能为空")
    @JsonProperty("bank_number")
    private String bankNumber;

    /**
     * 币别编码
     */
    @NotBlank(message = "币别编码不能为空")
    @JsonProperty("currency_number")
    private String currencyNumber;
}
