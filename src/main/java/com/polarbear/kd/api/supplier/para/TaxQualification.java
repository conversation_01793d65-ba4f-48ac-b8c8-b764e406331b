package com.polarbear.kd.api.supplier.para;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 税务资质类
 */
@Data
public class TaxQualification {
    /**
     * ID
     */
    private Long id;

    /**
     * 生效日期
     */
    @NotNull(message = "生效日期不能为空")
    @JsonProperty("effectivedate")
    private LocalDate effectiveDate;

    /**
     * 失效日期
     */
    @JsonProperty("expirydate")
    private LocalDate expiryDate;

    /**
     * 税务资质编码
     */
    @NotBlank(message = "税务资质编码不能为空")
    @JsonProperty("taxcertificate_number")
    private String taxCertificateNumber;
}