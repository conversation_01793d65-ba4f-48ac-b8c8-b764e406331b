package com.polarbear.kd.api.supplier.create;

import com.polarbear.kd.api.response.KdExtResponseData;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商信息类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierSaveResponseData extends KdExtResponseData {
    private Integer index;

    /**
     * 星空单据编号
     */
    private String billNum;

    /**
     * 星空单据来源单据编号
     */
    private String sourceBillNum;
}



