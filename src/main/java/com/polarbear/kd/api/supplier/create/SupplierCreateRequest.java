package com.polarbear.kd.api.supplier.create;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.api.SimpleResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

public class SupplierCreateRequest extends KdOpRequest<SupplierCreatePara> {
    @Override
    public String getUrlPath() {
        return "/v2/basedata/bd_supplier/add";
    }

    @Override
    public String logModule() {
        return "kd.bd_supplier.add";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return SimpleResponse.class;
    }

    @Override
    public KdOpRequest<SupplierCreatePara> setLogKey(Config<SupplierCreatePara> config) {
        config.setKey1(SupplierCreatePara::getNumber);
        config.setKey2(SupplierCreatePara::getName);
        return this;
    }
}
