package com.polarbear.kd.api.supplier.create;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

public class SupplierSaveRequest extends KdOpRequest<SupplierSavePara> {
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/basedata/bd_supplier/saveSupplier";
    }

    @Override
    public String logModule() {
        return "kd.bd_supplier.saveSupplier";
    }

    @Override
    public Class<SupplierSaveResponse> getResponseClass() {
        return SupplierSaveResponse.class;
    }

    @Override
    public KdOpRequest<SupplierSavePara> setLogKey(Config<SupplierSavePara> config) {
        config.setKey1(SupplierSavePara::getNumber);
        config.setKey2(SupplierSavePara::getGjwlAppid);
        config.setKey3(SupplierSavePara::getName);
        return this;
    }
}
