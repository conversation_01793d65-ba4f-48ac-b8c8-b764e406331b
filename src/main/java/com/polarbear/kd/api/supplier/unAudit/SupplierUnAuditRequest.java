package com.polarbear.kd.api.supplier.unAudit;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.api.supplier.unAudit.para.UnAuditPara;
import com.polarbear.kd.core.KdOpRequest;

public class SupplierUnAuditRequest extends KdOpRequest<UnAuditPara> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/basedata/bd_supplier/unAuditSupplier";
    }

    @Override
    public String logModule() {
        return "kd.bd_customer.unAuditSupplier";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return SupplierUnAuditResponse.class;
    }
}
