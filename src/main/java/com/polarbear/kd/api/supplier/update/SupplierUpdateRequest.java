package com.polarbear.kd.api.supplier.update;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.api.SimpleResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

public class SupplierUpdateRequest extends KdOpRequest<List<SupplierUpdatePara>> {
    @Override
    public String getUrlPath() {
        return "/v2/basedata/bd_supplier/batchUpdate";
    }

    @Override
    public String logModule() {
        return "kd.bd_supplier.batchUpdate";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return SimpleResponse.class;
    }

    @Override
    public KdOpRequest<List<SupplierUpdatePara>> setLogKey(Config<List<SupplierUpdatePara>> config) {
        config.setKey1(o -> o.size() == 1 ? o.get(0).getNumber() : "")
                .setKey2(o -> o.size() == 1 ? o.get(0).getName() : "");
        return this;
    }
}
