package com.polarbear.kd.api.supplier.para;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 供应商信息类
 */
@Data
public class Supplier {
    /**
     * 编码
     */
    @NotBlank(message = "编码不能为空")
    @JsonProperty("number")
    private String number;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @JsonProperty("name")
    private String name;

    /**
     * 创建时间
     */
    @JsonProperty("createtime")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonProperty("modifytime")
    private LocalDateTime modifyTime;

    /**
     * 主数据内码
     */
    @JsonProperty("masterid")
    private Long masterId;

    /**
     * 简称
     */
    @JsonProperty("simplename")
    private String simpleName;

    /**
     * 伙伴类型
     */
    @JsonProperty("type")
    private String type;

    /**
     * 详细地址
     */
    @JsonProperty("bizpartner_address")
    private String bizPartnerAddress;

    /**
     * 联系人
     */
    @JsonProperty("linkman")
    private String linkman;

    /**
     * 联系电话
     */
    @JsonProperty("bizpartner_phone")
    private String bizPartnerPhone;

    /**
     * 传真
     */
    @JsonProperty("bizpartner_fax")
    private String bizPartnerFax;

    /**
     * 统一社会信用代码
     */
    @JsonProperty("societycreditcode")
    private String societyCreditCode;

    /**
     * 纳税人识别号
     */
    @JsonProperty("tx_register_no")
    private String txRegisterNo;

    /**
     * 法人代表
     */
    @JsonProperty("artificialperson")
    private String artificialPerson;

    /**
     * 注册资本
     */
    @JsonProperty("regcapital")
    private String regCapital;

    /**
     * 营业期限
     */
    @JsonProperty("businessterm")
    private String businessTerm;

    /**
     * 经营范围
     */
    @JsonProperty("businessscope")
    private String businessScope;

    /**
     * 成立日期
     */
    @JsonProperty("establishdate")
    private LocalDate establishDate;

    /**
     * 业务职能
     */
    @JsonProperty("bizfunction")
    private String bizFunction;

    /**
     * 邓白氏编码
     */
    @JsonProperty("duns")
    private String duns;

    /**
     * 创建人编码
     */
    @NotBlank(message = "创建人编码不能为空")
    @JsonProperty("creator_number")
    private String creatorNumber;

    /**
     * 创建组织编码
     */
    @NotBlank(message = "创建组织编码不能为空")
    @JsonProperty("createorg_number")
    private String createOrgNumber;

    /**
     * 修改人编码
     */
    @NotBlank(message = "修改人编码不能为空")
    @JsonProperty("modifier_number")
    private String modifierNumber;

    /**
     * 交易币别编码
     */
    @JsonProperty("settlementcyid_number")
    private String settlementCyIdNumber;

    /**
     * 结算方式编码
     */
    @JsonProperty("settlementtypeid_number")
    private String settlementTypeIdNumber;

    /**
     * 付款条件编码
     */
    @JsonProperty("paycond_number")
    private String payCondNumber;

    /**
     * 国家地区编码
     */
    @JsonProperty("country_number")
    private String countryNumber;

    /**
     * 内部业务单元编码
     */
    @JsonProperty("internal_company_number")
    private String internalCompanyNumber;

    /**
     * 默认税率编码
     */
    @JsonProperty("taxrate_number")
    private String taxRateNumber;

    /**
     * 付款币别编码
     */
    @JsonProperty("paymentcurrency_number")
    private String paymentCurrencyNumber;

    /**
     * 税务注册地编码
     */
    @JsonProperty("taxregistplace_number")
    private String taxRegistPlaceNumber;

    /**
     * 注册资本币种编码
     */
    @JsonProperty("curegcapital_number")
    private String curRegCapitalNumber;
}



