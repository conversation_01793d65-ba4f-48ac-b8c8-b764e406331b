package com.polarbear.kd.api.supplier.create;


import com.polarbear.kd.api.supplier.para.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.List;


/**
 * 供应商信息类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierCreatePara extends Supplier {
    /**
     * 联系人分录
     */
    @Valid
    private List<Linkman> entryLinkman;

    /**
     * 银行信息分录
     */
    @Valid
    private List<BankInfo> entryBank;

    /**
     * 分类标准
     */
    @Valid
    private List<GroupStandard> entryGroupStandard;

    /**
     * 税务资质
     */
    @Valid
    private List<TaxQualification> entryTax;
}



