package com.polarbear.kd.api;


import com.polarbear.kd.api.response.HrResponseData;
import com.polarbear.kd.exception.KingDeeOpException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = true)
public class HrResponse<T> extends KdResponse<HrResponseData<T>> {
    private String errorCode;
    private String message;
    private Boolean status;
    private HrResponseData<T> data;

    public void check() {
        if (!status) {
            throw new KingDeeOpException(errorCode, "kingdee api 请求失败,异常:" + message);
        }
        if (!"0".equals(errorCode)) {
            throw new KingDeeOpException(errorCode, "kingdee api 请求失败,异常:" + message);
        }
        if (data != null) {
            HrResponseData<T> hrResponseData = data;
            if (!hrResponseData.getSucess()) {
                throw new KingDeeOpException(hrResponseData.getCode(), "kingdee api 请求失败,异常:" + Optional.ofNullable(hrResponseData.getMsg()).orElse(""));
            }
            if (hrResponseData.getFail()) {
                throw new KingDeeOpException(hrResponseData.getCode(), "kingdee api 请求失败,异常:" + Optional.ofNullable(hrResponseData.getMsg()).orElse(""));
            }
        }
    }
}
