package com.polarbear.kd.api.material;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;
import java.util.List;

/**
 * 物料库存信息批量更新接口
 *
 * <AUTHOR>
 * @apiNote 广交云.【产品】.新增后，如物料需要开启保质期则需要调用该接口 推送 金蝶云·星空旗舰版.【物料】
 */
public class MaterialInventoryUpdateRequest extends KdOpRequest<List<MaterialInventoryUpdatePara>> {

  @Override
  public String getUrlPath() {
    return "/v2/gjwl/sbd/bd_materialinventoryinfo/batchUpdateNew";
  }

  @Override
  public String logModule() {
    return "kd.bd_material.inventoryUpdate";
  }

  @Override
  public Class<MaterialInventoryUpdateResponse> getResponseClass() {
    return MaterialInventoryUpdateResponse.class;
  }

  @Override
  public KdOpRequest<List<MaterialInventoryUpdatePara>> setLogKey(
      Config<List<MaterialInventoryUpdatePara>> config) {
    config.setKey1(
        o -> {
          if (o != null && !o.isEmpty()) {
            return o.get(0).getMasteridNumber();
          }
          return "unknown";
        });
    config.setKey2(
        o -> {
          if (o != null && !o.isEmpty()) {
            return o.get(0).getCreateorgNumber();
          }
          return "unknown";
        });
    return this;
  }
}
