package com.polarbear.kd.api.material;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;
import java.util.List;

/**
 * 物料和组织公共信息批量保存提交审核接口
 *
 * <AUTHOR>
 * @apiNote 广交云.【产品】.审核后 推送 金蝶云·星空旗舰版.【物料】
 */
public class MaterialSaveRequest extends KdOpRequest<MaterialSaveRequest.MaterialSaveData> {

  @Override
  public String getUrlPath() {
    return "/v2/gjwl/gjwl_basedata_ext/saveMaterial";
  }

  @Override
  public String logModule() {
    return "kd.bd_material.saveMaterial";
  }

  @Override
  public Class<MaterialSaveResponse> getResponseClass() {
    return MaterialSaveResponse.class;
  }

  @Override
  public KdOpRequest<MaterialSaveData> setLogKey(Config<MaterialSaveData> config) {
    config.setKey1(
        o -> {
          if (o.getMaterials() != null && !o.getMaterials().isEmpty()) {
            MaterialDataVo firstMaterial = o.getMaterials().get(0);
            if (firstMaterial.getMaterialInfoVo() != null) {
              return firstMaterial.getMaterialInfoVo().getName();
            }
          }
          return "unknown";
        });
    config.setKey2(
        o -> {
          if (o.getMaterials() != null && !o.getMaterials().isEmpty()) {
            MaterialDataVo firstMaterial = o.getMaterials().get(0);
            if (firstMaterial.getMaterialInfoVo() != null) {
              return firstMaterial.getMaterialInfoVo().getNumber();
            }
          }
          return "unknown";
        });
    return this;
  }

  /** 物料保存请求数据 */
  public static class MaterialSaveData {
    private List<MaterialDataVo> materials;

    public List<MaterialDataVo> getMaterials() {
      return materials;
    }

    public void setMaterials(List<MaterialDataVo> materials) {
      this.materials = materials;
    }
  }
}
