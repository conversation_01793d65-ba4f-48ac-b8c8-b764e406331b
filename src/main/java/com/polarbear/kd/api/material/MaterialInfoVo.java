package com.polarbear.kd.api.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 物料主档信息VO
 *
 * <AUTHOR>
 */
@Data
public class MaterialInfoVo {

  /** 物料ID */
  @JsonProperty("id")
  private String id;

  /** 物料名称 */
  @JsonProperty("name")
  private String name;

  /** 物料编码 */
  @JsonProperty("number")
  private String number;

  /** 外部编码 */
  @JsonProperty("gjwl_appid")
  private String gjwlAppid;

  /** 来源系统 固定值，传"广交云供应链管理系统" */
  @JsonProperty("gjwl_sourcesystemtype")
  private String gjwlSourcesystemtype;

  /** 创建组织(编码) - 固定值，传"gjwl" */
  @JsonProperty("createorg")
  private String createorg;

  /** 基本单位(编码) */
  @JsonProperty("baseunit")
  private String baseunit;

  /** 规格型号 */
  @JsonProperty("modelnum")
  private String modelnum;

  /** 换算方向 A正向换算 B逆向换算 - 固定值，传"A" */
  @JsonProperty("unitconvertdir")
  private String unitconvertdir;

  /** 分类信息分录 */
  @JsonProperty("entry_groupstandard")
  private List<GroupStandardEntry> entryGroupstandard;
}
