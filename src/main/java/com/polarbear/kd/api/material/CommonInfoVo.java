package com.polarbear.kd.api.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 物料组织公共信息VO
 *
 * <AUTHOR>
 */
@Data
public class CommonInfoVo {

  /** 物料类型 1物资 7费用 8资产 9服务 - 固定值，传"1" */
  @JsonProperty("materialtype")
  private String materialtype;

  /** 物料属性 10030自制 10040外购 10050委外 10020虚拟 - 固定值，传"10040" */
  @JsonProperty("materialattr")
  private String materialattr;

  /** 采购信息 - 固定值，传"true" */
  @JsonProperty("enablepur")
  private Boolean enablepur;

  /** 销售信息 - 固定值，传"true" */
  @JsonProperty("enablesale")
  private Boolean enablesale;

  /** 库存信息 - 固定值，传"true" */
  @JsonProperty("enableinv")
  private Boolean enableinv;

  /**
   * 存货类别 CHJZFL01-SYS原材料 CHJZFL02-SYS辅料 CHJZFL03-SYS自制半成品 CHJZFL04-SYS委外半成品 CHJZFL05-SYS产成品
   * CHJZFL06-SYS库存商品 - 固定值，传"CHJZFL06-SYS"
   */
  @JsonProperty("group")
  private String group;

  public static CommonInfoVo getDefault() {
    CommonInfoVo vo = new CommonInfoVo();
    vo.setMaterialtype("1");
    vo.setMaterialattr("10040");
    vo.setEnablepur(true);
    vo.setEnablesale(true);
    vo.setEnableinv(true);
    vo.setGroup("CHJZFL06-SYS");
    return vo;
  }
}
