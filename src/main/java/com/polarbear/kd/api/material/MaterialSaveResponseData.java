package com.polarbear.kd.api.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 物料保存响应数据
 *
 * <AUTHOR>
 */
@Data
public class MaterialSaveResponseData {

  /** 返回结果详细信息 */
  @JsonProperty("result")
  private MaterialSaveResult result;

  /** 接口最终错误码，按该字段判断接口是否成功 */
  @JsonProperty("finalErrorCode")
  private String finalErrorCode;

  /** 接口调用最终错误信息，按该字段获取接口调用错误信息 */
  @JsonProperty("finalMessage")
  private String finalMessage;

  /** 接口最终访问是否成功，按该字段判断接口是否成功 */
  @JsonProperty("finalStatus")
  private Boolean finalStatus;

  /** 物料保存结果 */
  @Data
  public static class MaterialSaveResult {

    /** 保存返回结果详细信息 */
    @JsonProperty("saveResult")
    private Object saveResult;

    /** 提交返回结果详细信息 */
    @JsonProperty("submitResult")
    private List<OperationResult> submitResult;

    /** 审核返回结果详细信息 */
    @JsonProperty("auditResult")
    private List<OperationResult> auditResult;
  }

  /** 保存结果 */
  @Data
  public static class SaveResult {

    /** 保存是否成功 */
    @JsonProperty("result")
    private Boolean result;

    /** 返回结果详细信息 */
    @JsonProperty("resultVo")
    private List<SaveResultVo> resultVo;

    /** 描述 */
    @JsonProperty("desc")
    private String desc;
  }

  /** 保存结果详细信息 */
  @Data
  public static class SaveResultVo {

    /** 物料组织公共信息Id */
    @JsonProperty("commonInfoId")
    private String commonInfoId;

    /** 物料编码 */
    @JsonProperty("materialNumber")
    private String materialNumber;

    /** 是否成功 */
    @JsonProperty("success")
    private Boolean success;

    /** 物料Id */
    @JsonProperty("materialId")
    private String materialId;

    /** 消息 */
    @JsonProperty("message")
    private String message;

    /** 组织编码 */
    @JsonProperty("orgNumber")
    private String orgNumber;

    /** 组织Id */
    @JsonProperty("orgId")
    private String orgId;
  }

  /** 操作结果（提交/审核） */
  @Data
  public static class OperationResult {

    /** 物料编码 */
    @JsonProperty("number")
    private String number;

    /** 操作是否成功 */
    @JsonProperty("billStatus")
    private Boolean billStatus;

    /** 物料Id */
    @JsonProperty("id")
    private String id;

    /** 错误信息数据 */
    @JsonProperty("errors")
    private List<Object> errors;
  }
}
