package com.polarbear.kd.api.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 分类信息分录
 *
 * <AUTHOR>
 */
@Data
public class GroupStandardEntry {

  /** 分类标准创建组织(编码) - 固定值，传"gjwl" */
  @JsonProperty("groupStrandardCreateOrg")
  private String groupStrandardCreateOrg;

  /** 分类标准编码 - 固定值，传"JBFLBZ" */
  @JsonProperty("groupStrandardNumber")
  private String groupStrandardNumber;

  /** 分类编码 - 分类编码，广交云和星空维护一致 */
  @JsonProperty("groupNumber")
  private String groupNumber;
}
