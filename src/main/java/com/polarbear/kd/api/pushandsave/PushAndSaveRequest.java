package com.polarbear.kd.api.pushandsave;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

/**
 * 下推并保存接口
 * 
 * <AUTHOR>
 * @apiNote 用于单据下推的场景：
 * 1. 广交云.【销售出库单】.审核后需下推金蝶云·星空旗舰版.生成【销售出库单】
 * 2. 广交云.【销售退货单】.审核后需下推金蝶云·星空旗舰版.生成【销售退货单】
 * 3. 广交云.【采购入库单】.审核后需下推金蝶云·星空旗舰版.生成【采购入库单】
 * 4. 广交云.【采退出库单】.审核后需下推金蝶云·星空旗舰版.生成【采购退料单】
 * 5. 广交云.【调拨单(调出)】.审核后需下推金蝶云·星空旗舰版.生成【分步式调入单】
 * 
 * 说明：调用该接口后单据为暂存状态，还需要调用对应的保存接口才会执行保存提交审核操作。
 * @date 2025-08-28
 */
public class PushAndSaveRequest extends KdOpRequest<PushAndSaveRequestParam> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/msbd/pushAndSave";
    }

    @Override
    public String logModule() {
        return "kd.msbd.pushAndSave";
    }

    @Override
    public Class<PushAndSaveResponse> getResponseClass() {
        return PushAndSaveResponse.class;
    }

    @Override
    public KdOpRequest<PushAndSaveRequestParam> setLogKey(Config<PushAndSaveRequestParam> config) {
        config.setKey1(o -> {
            if (o != null && o.getSourceBills() != null && !o.getSourceBills().isEmpty()) {
                String keys = String.join(",", o.getSourceBills().stream()
                    .map(SelectedBill::getBillId)
                    .toArray(String[]::new));
                return keys.substring(0, Math.min(keys.length(), 250));
            }
            return "pushAndSave";
        });
        return this;
    }
}
