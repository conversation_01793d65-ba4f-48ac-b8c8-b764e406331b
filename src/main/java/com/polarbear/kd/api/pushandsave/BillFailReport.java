package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 单据失败报告
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class BillFailReport {
    
    /**
     * 单据ID
     */
    @JsonProperty("billId")
    private String billId;
    
    /**
     * 单据编号
     */
    @JsonProperty("billNo")
    private String billNo;
    
    /**
     * 失败原因
     */
    @JsonProperty("failMessage")
    private String failMessage;
    
    /**
     * 分录失败详情
     */
    @JsonProperty("rowFailMessages")
    private List<RowFailMessage> rowFailMessages;
}
