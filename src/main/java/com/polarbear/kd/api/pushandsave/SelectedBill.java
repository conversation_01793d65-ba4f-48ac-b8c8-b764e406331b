package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 源单单据信息
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class SelectedBill {
    
    /**
     * 源单ID
     */
    @JsonProperty("billId")
    private String billId;
    
    /**
     * 源单分录ID集合，为空则整单下推
     */
    @JsonProperty("entryIds")
    private List<String> entryIds;
}
