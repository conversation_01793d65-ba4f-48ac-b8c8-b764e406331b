package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 下推结果
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class PushApiResult {
    
    /**
     * 返回结果详细信息
     */
    @JsonProperty("result")
    private List<Map<String, Object>> result;
    
    /**
     * 下推并保存返回结果详细信息
     */
    @JsonProperty("pushResult")
    private List<PushResult> pushResult;
    
    /**
     * 下推并保存返回目标单据详细信息
     */
    @JsonProperty("targetResult")
    private List<TargetResult> targetResult;
}
