package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 下推并保存返回目标单据详细信息
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class TargetResult {
    
    /**
     * 目标单据ID，需记录，用于修改单据
     */
    @JsonProperty("fid")
    private String fid;
    
    /**
     * 目标单据编号
     */
    @JsonProperty("fbillno")
    private String fbillno;
    
    /**
     * 序号
     */
    @JsonProperty("fseq")
    private String fseq;
    
    /**
     * 目标单据分录ID，需记录，用于修改单据
     */
    @JsonProperty("fentryid")
    private String fentryid;
    
    /**
     * 物料ID
     */
    @JsonProperty("fmasterid")
    private String fmasterid;
    
    /**
     * 物料编码
     */
    @JsonProperty("fnumber")
    private String fnumber;
}
