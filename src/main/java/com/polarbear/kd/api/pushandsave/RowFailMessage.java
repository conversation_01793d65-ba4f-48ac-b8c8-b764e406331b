package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 分录失败详情
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class RowFailMessage {
    
    /**
     * 分录ID
     */
    @JsonProperty("entryRowId")
    private String entryRowId;
    
    /**
     * 分录索引
     */
    @JsonProperty("rowIndex")
    private Integer rowIndex;
    
    /**
     * 失败原因
     */
    @JsonProperty("failMessage")
    private String failMessage;
}
