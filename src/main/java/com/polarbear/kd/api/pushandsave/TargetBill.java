package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 生成的目标单信息
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class TargetBill {
    
    /**
     * 目标单ID
     */
    @JsonProperty("billId")
    private String billId;
    
    /**
     * 目标单单据编号
     */
    @JsonProperty("billNo")
    private String billNo;
    
    /**
     * 关联源单信息
     */
    @JsonProperty("sourceBills")
    private List<SourceBill> sourceBills;
}
