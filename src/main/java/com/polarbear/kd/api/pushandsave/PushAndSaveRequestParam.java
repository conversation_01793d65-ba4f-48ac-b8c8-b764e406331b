package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 下推并保存接口请求参数
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class PushAndSaveRequestParam {
    
    /**
     * 源单单据标识
     * 如为销售订单下推销售出库单时：传sm_salorder；
     * 如为销售出库单下推销售退货单时：传im_saloutbill；
     * 如为采购订单下推采购入库单时：传pm_purorderbill；
     * 如为采购入库单下推采购退料单时：传im_purinbill；
     * 如为分步调出单下推分步调入单时：传im_transoutbill；
     */
    @JsonProperty("sourceEntityNumber")
    private String sourceEntityNumber;
    
    /**
     * 目标单单据标识
     * 如为销售订单下推销售出库单时：传im_saloutbill；
     * 如为销售出库单下推销售退货时：传im_saloutbill；
     * 如为采购订单下推采购入库单时：传im_purinbill；
     * 如为采购入库单下推采购退料单时：传im_purinbill；
     * 分步调出单下推分步调入单时：传im_transinbill；
     */
    @JsonProperty("targetEntityNumber")
    private String targetEntityNumber;
    
    /**
     * 转换规则ID
     * 如为销售订单下推销售出库单时：传610056021305574400；
     * 如为销售出库单下推销售退货单时：传705622291240812544；
     * 如为采购订单下推采购入库单时：传565033700123759616；
     * 如为采购入库单下推采购退料单时：传705591916980436992；
     * 如为分步调出单下推分步调入单时：传475190152356975616；
     */
    @JsonProperty("ruleId")
    private String ruleId;
    
    /**
     * 源单单据信息，支持整单下推、按分录下推、合并下推
     */
    @JsonProperty("sourceBills")
    private List<SelectedBill> sourceBills;
}
