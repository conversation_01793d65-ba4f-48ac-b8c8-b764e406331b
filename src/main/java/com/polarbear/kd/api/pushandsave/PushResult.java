package com.polarbear.kd.api.pushandsave;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 下推并保存返回结果详细信息
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Data
public class PushResult {
    
    /**
     * 下推是否成功
     */
    @JsonProperty("pushSuccess")
    private Boolean pushSuccess;
    
    /**
     * 下推失败整体提示
     */
    @JsonProperty("pushFailMessage")
    private String pushFailMessage;
    
    /**
     * 源单下推失败报告
     */
    @JsonProperty("pushFailReports")
    private List<BillFailReport> pushFailReports;
    
    /**
     * 目标单保存是否成功
     */
    @JsonProperty("saveSuccess")
    private Boolean saveSuccess;
    
    /**
     * 目标单保存失败整体提示
     */
    @JsonProperty("saveFailMessage")
    private String saveFailMessage;
    
    /**
     * 目标单保存失败报告
     */
    @JsonProperty("saveFailReports")
    private List<BillFailReport> saveFailReports;
    
    /**
     * 生成的目标单信息
     */
    @JsonProperty("targetBills")
    private List<TargetBill> targetBills;
}
