package com.polarbear.kd.api;


import com.polarbear.kd.api.response.KdExtResponseData;
import com.polarbear.kd.exception.KingDeeOpException;
import com.polarbear.springframework.boot.service.exception.ServiceException;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class KdExtResponse<T extends KdExtResponseData> extends EmptyResponse {
    private String errorCode;
    private String message;
    private Boolean status;
    private List<T> data;

    public void check() {
        if (!status) {
            throw new KingDeeOpException(errorCode, "kingdee api 请求失败,异常:" + message);
        }
        if (!"0".equals(errorCode)) {
            throw new KingDeeOpException(errorCode, "kingdee api 请求失败,异常:" + message);
        }
        if (data != null && data.size() == 1) {
            if (!data.get(0).getSuccess()) {
                throw new KingDeeOpException("E999", "kingdee api 请求失败,异常:" + data.get(0).getMessage());
            }
        }
    }
}
