package com.polarbear.kd.api;


import com.polarbear.kd.exception.KingDeeOpException;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KdResponse<T> extends EmptyResponse {
    private String errorCode;
    private String message;
    private Boolean status;
    private T data;

    public void check() {
        if (!status) {
            throw new KingDeeOpException(errorCode, "kingdee api 请求失败,异常:" + message);
        }
        if (!"0".equals(errorCode)) {
            throw new KingDeeOpException(errorCode, message);
        }
    }
}
