package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 其他出库单明细实体类
 * 
 * <AUTHOR>
 */
@Data
public class OtherOutBillEntry {
    
    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料编码.编码 (广交产品编码)
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 库存单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 物料明细.数量
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 出库库存类型.编码 (固定值，传"110")
     * 110-普通、111-赠品、113-VMI
     */
    @JsonProperty("outinvtype_number")
    private String outinvtypeNumber;
    
    /**
     * 出库库存状态.编码 (固定值，传"110")
     */
    @JsonProperty("outinvstatus_number")
    private String outinvstatusNumber;
    
    /**
     * 物料明细.出库货主类型 (固定值，传"bos_org")
     * bd_supplier:供应商, bd_customer:客户, bos_org:核算组织
     */
    @JsonProperty("outownertype")
    private String outownertype;
    
    /**
     * 出库货主.编码 (固定值，传"1011")
     */
    @JsonProperty("outowner_number")
    private String outownerNumber;
    
    /**
     * 物料明细.出库保管者类型 (固定值，传"bos_org")
     * bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("outkeepertype")
    private String outkeepertype;
    
    /**
     * 出库保管者.编码 (固定值，传"1011")
     */
    @JsonProperty("outkeeper_number")
    private String outkeeperNumber;
    
    /**
     * 项目号.项目编码
     */
    @JsonProperty("project_number")
    private String projectNumber;
    
    /**
     * 物料明细.批号 (对应广交云批次，物料启用批号时必传)
     */
    @JsonProperty("lotnumber")
    private String lotnumber;
    
    /**
     * 物料明细.生产批号 (对应广交云生产批号)
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
    
    /**
     * 物料明细.生产日期 (物料启用保质期时必传)
     */
    @JsonProperty("producedate")
    private String producedate;
    
    /**
     * 物料明细.到期日期 (物料启用保质期时必传)
     */
    @JsonProperty("expirydate")
    private String expirydate;
    
    /**
     * 入库库存类型.编码 (固定值，传"110")
     */
    @JsonProperty("invtype_number")
    private String invtypeNumber;
    
    /**
     * 入库库存状态.编码 (固定值，传"110")
     */
    @JsonProperty("invstatus_number")
    private String invstatusNumber;
    
    /**
     * 货主类型 (固定值，传"bos_org")
     */
    @JsonProperty("ownertype")
    private String ownertype;
    
    /**
     * 货主.编码 (固定值，传"1011")
     */
    @JsonProperty("owner_number")
    private String ownerNumber;
    
    /**
     * 保管者类型 (固定值，传"bos_org")
     */
    @JsonProperty("keepertype")
    private String keepertype;
    
    /**
     * 保管者.编码 (固定值，传"1011")
     */
    @JsonProperty("keeper_number")
    private String keeperNumber;
    
    /**
     * 价格
     */
    @JsonProperty("price")
    private BigDecimal price;
    
    /**
     * 分录备注
     */
    @JsonProperty("entrycomment")
    private String entrycomment;
}
