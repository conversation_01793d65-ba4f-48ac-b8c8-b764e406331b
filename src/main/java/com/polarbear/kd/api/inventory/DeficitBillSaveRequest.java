package com.polarbear.kd.api.inventory;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 盘亏单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【报损单(盘亏)】.审核后 推送 金蝶云·星空旗舰版.【盘亏单】
 */
public class DeficitBillSaveRequest extends KdOpRequest<List<DeficitBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/im_deficitbill/saveDeficitBill";
    }

    @Override
    public String logModule() {
        return "kd.im_deficitbill.saveDeficitBill";
    }

    @Override
    public Class<DeficitBillSaveResponse> getResponseClass() {
        return DeficitBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<DeficitBill>> setLogKey(Config<List<DeficitBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(DeficitBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
