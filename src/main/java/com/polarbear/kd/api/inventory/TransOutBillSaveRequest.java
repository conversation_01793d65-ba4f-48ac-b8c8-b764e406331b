package com.polarbear.kd.api.inventory;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 分步调出单保存接口
 *
 * <AUTHOR>
 * @apiNote 广交云.【调拨单(调出)】.审核后 推送 金蝶云·星空旗舰版.【分步调出单】
 */
public class TransOutBillSaveRequest extends KdOpRequest<List<TransOutBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/im_transoutbill/saveTransOutBill";
    }

    @Override
    public String logModule() {
        return "kd.im_transoutbill.SaveTransOutBill";
    }

    @Override
    public Class<TransOutBillSaveResponse> getResponseClass() {
        return TransOutBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<TransOutBill>> setLogKey(Config<List<TransOutBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(TransOutBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
