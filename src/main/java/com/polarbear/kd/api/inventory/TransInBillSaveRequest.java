package com.polarbear.kd.api.inventory;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 分步调入单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【调拨单(调入)】.修改操作.审核后 推送 金蝶云·星空旗舰版.【分步调入单】
 */
public class TransInBillSaveRequest extends KdOpRequest<List<TransInBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/im/im_transinbill/saveTransInBill";
    }

    @Override
    public String logModule() {
        return "kd.im_transinbill.saveTransInBill";
    }

    @Override
    public Class<TransInBillSaveResponse> getResponseClass() {
        return TransInBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<TransInBill>> setLogKey(Config<List<TransInBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(TransInBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
