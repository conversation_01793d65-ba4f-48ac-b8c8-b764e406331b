package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 盘亏单明细实体类
 * 
 * <AUTHOR>
 */
@Data
public class DeficitBillEntry {
    
    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料编码.编码
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 出库库存状态.编码 (固定值，传"110")
     */
    @JsonProperty("outinvstatus_number")
    private String outinvstatusNumber;
    
    /**
     * 出库库存类型.编码 (固定值，传"110")
     */
    @JsonProperty("outinvtype_number")
    private String outinvtypeNumber;
    
    /**
     * 货主.编码
     */
    @JsonProperty("outowner_number")
    private String outownerNumber;
    
    /**
     * 保管者.编码
     */
    @JsonProperty("outkeeper_number")
    private String outkeeperNumber;
    
    /**
     * 物料明细.货主类型 (固定值，传"bos_org")
     * bos_org:业务组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("outownertype")
    private String outownertype;
    
    /**
     * 物料明细.保管者类型 (固定值，传"bos_org")
     * bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("outkeepertype")
    private String outkeepertype;
    
    /**
     * 库存单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 物料明细.盘点数量（库存）
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 物料明细.账存数量（库存）
     */
    @JsonProperty("invqtyacc")
    private BigDecimal invqtyacc;
    
    /**
     * 物料明细.盘亏数量（库存） (传差异数量)
     */
    @JsonProperty("invlossqty")
    private BigDecimal invlossqty;
    
    /**
     * 基本单位.编码
     */
    @JsonProperty("baseunit_number")
    private String baseunitNumber;

    /**
     * 物料明细.批号 (对应广交云批次)
     */
    @JsonProperty("lotnumber")
    private String lotnumber;

    /**
     * 物料明细.生产批号 (对应广交云生产批号)
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;

    /**
    * 物料明细.生产日期
    */
    @JsonProperty("producedate")
    private String producedate;
    
    /**
    * 物料明细.到期日期
    */
    @JsonProperty("expirydate")
    private String expirydate;
}
