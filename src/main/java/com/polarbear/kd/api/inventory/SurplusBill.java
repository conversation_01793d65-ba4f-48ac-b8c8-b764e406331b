package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 盘盈单实体类
 *
 * <AUTHOR>
 * @apiNote 广交云.【报溢单】.审核后 推送 金蝶云·星空旗舰版.【盘盈单】
 */
@Data
public class SurplusBill {

  /** 库存组织.编码 (固定值，传"1011") */
  @JsonProperty("org_number")
  private String orgNumber;

  /** 单据编号 */
  @JsonProperty("billno")
  private String billno;

  /** 外部系统单号 (传广交云单号) */
  @JsonProperty("gjwl_thirdparty_billno")
  private String gjwlThirdpartyBillno;

  /** 来源系统 */
  @JsonProperty("gjwl_sourcesystemtype")
  private String gjwlSourcesystemtype;

  /** 记账日期 */
  @JsonProperty("bookdate")
  private String bookdate;

  /** 单据类型.编码 (固定值，传"im_InvCheckIn_STD_BT_S") */
  @JsonProperty("billtype_number")
  private String billtypeNumber;

  /** 业务类型.编码 (固定值，传"350") */
  @JsonProperty("biztype_number")
  private String biztypeNumber;

  /** 业务日期 */
  @JsonProperty("biztime")
  private String biztime;

  /** 库存事务.编码 (固定值，传"350") */
  @JsonProperty("invscheme_number")
  private String invschemeNumber;

  /** 备注 */
  @JsonProperty("comment")
  private String comment;

  /** 物料明细 */
  @JsonProperty("billentry")
  private List<SurplusBillEntry> billentry;
}
