package com.polarbear.kd.api.inventory;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 盘盈单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【报溢单】.审核后 推送 金蝶云·星空旗舰版.【盘盈单】
 */
public class SurplusBillSaveRequest extends KdOpRequest<List<SurplusBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/im_surplusbill/saveSurpluseBill";
    }

    @Override
    public String logModule() {
        return "kd.im_surplusbill.saveSurpluseBill";
    }

    @Override
    public Class<SurplusBillSaveResponse> getResponseClass() {
        return SurplusBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<SurplusBill>> setLogKey(Config<List<SurplusBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(SurplusBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
