package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 盘盈单明细实体类
 * 
 * <AUTHOR>
 */
@Data
public class SurplusBillEntry {
    
    /**
     * 行类型.编码 (固定值，传"010")
     */
    @JsonProperty("linetype_number")
    private String linetypeNumber;
    
    /**
     * 物料编码.编码 (广交产品编码)
     */
    @JsonProperty("material_number")
    private String materialNumber;
    
    /**
     * 入库库存状态.编码 (固定值，传"110")
     */
    @JsonProperty("invstatus_number")
    private String invstatusNumber;
    
    /**
     * 入库库存类型.编码 (固定值，传"110")
     */
    @JsonProperty("invtype_number")
    private String invtypeNumber;
    
    /**
     * 货主.编码 (固定值，传"1011")
     */
    @JsonProperty("owner_number")
    private String ownerNumber;
    
    /**
     * 保管者.编码 (固定值，传"1011")
     */
    @JsonProperty("keeper_number")
    private String keeperNumber;
    
    /**
     * 物料明细.货主类型 (固定值，传"bos_org")
     * bos_org:业务组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("ownertype")
    private String ownertype;
    
    /**
     * 物料明细.保管者类型 (固定值，传"bos_org")
     * bos_org:库存组织, bd_supplier:供应商, bd_customer:客户
     */
    @JsonProperty("keepertype")
    private String keepertype;
    
    /**
     * 库存单位.编码
     */
    @JsonProperty("unit_number")
    private String unitNumber;
    
    /**
     * 仓库.编码
     */
    @JsonProperty("warehouse_number")
    private String warehouseNumber;
    
    /**
     * 物料明细.盘点数量（库存）
     */
    @JsonProperty("qty")
    private BigDecimal qty;
    
    /**
     * 物料明细.盘盈数量（库存） (传差异数量)
     */
    @JsonProperty("invgainqty")
    private BigDecimal invgainqty;
    
    /**
     * 物料明细.账存数量（库存）
     */
    @JsonProperty("invqtyacc")
    private BigDecimal invqtyacc;
    
    /**
     * 基本单位.编码 (同库存单位.编码)
     */
    @JsonProperty("baseunit_number")
    private String baseunitNumber;

    /**
     * 物料明细.批号 (对应广交云批次)
     */
    @JsonProperty("lotnumber")
    private String lotnumber;

    /**
     * 物料明细.生产批号 (对应广交云生产批号)
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;

    /**
     * 生产日期
     */
    @JsonProperty("producedate")
    private String producedate;

    /**
     * 有效期至
     */
    @JsonProperty("expirydate")
    private String expirydate;
}
