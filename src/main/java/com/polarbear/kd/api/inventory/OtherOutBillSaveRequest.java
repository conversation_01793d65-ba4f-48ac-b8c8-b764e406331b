package com.polarbear.kd.api.inventory;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 其他出库单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【报损单(不合格报损)】.审核后 推送 金蝶云·星空旗舰版.【其他出库单】
 */
public class OtherOutBillSaveRequest extends KdOpRequest<List<OtherOutBill>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/im/im_otheroutbill/saveOtherOutBill";
    }

    @Override
    public String logModule() {
        return "kd.im_otheroutbill.saveOtherOutBill";
    }

    @Override
    public Class<OtherOutBillSaveResponse> getResponseClass() {
        return OtherOutBillSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<OtherOutBill>> setLogKey(Config<List<OtherOutBill>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(OtherOutBill::getGjwlThirdpartyBillno).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
