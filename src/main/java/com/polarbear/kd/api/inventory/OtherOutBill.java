package com.polarbear.kd.api.inventory;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 其他出库单实体类
 *
 * <AUTHOR>
 * @apiNote 广交云.【报损单(不合格报损)】.审核后 推送 金蝶云·星空旗舰版.【其他出库单】
 */
@Data
public class OtherOutBill {

    /**
     * 库存组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;

    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billno;

    /**
     * 外部系统单号 (传广交云单号)
     */
    @JsonProperty("gjwl_thirdparty_billno")
    private String gjwlThirdpartyBillno;

    /**
     * 来源系统 (固定值，传"广交云供应链管理系统")
     */
    @JsonProperty("gjwl_sourcesystemtype")
    private String gjwlSourcesystemtype;

    /**
     * 业务日期
     */
    @JsonProperty("biztime")
    private String biztime;

    /**
     * 记账日期
     */
    @JsonProperty("bookdate")
    private String bookdate;

    /**
     * 单据类型.编码 (固定值，传"im_OtherOutBill_STD_BT_S")
     */
    @JsonProperty("billtype_number")
    private String billtypeNumber;

    /**
     * 业务类型.编码 (固定值，传"355")
     * 355-其他出库、322-资产领料、3551-其他出库退回、356-VMI其他出库
     */
    @JsonProperty("biztype_number")
    private String biztypeNumber;

    /**
     * 库存事务.编码 (固定值，传"355")
     * 355-其他出库、3551-其他出库退、356-VMI其他出库
     */
    @JsonProperty("invscheme_number")
    private String invschemeNumber;

    /**
     * 领用部门.编码 (固定值，先传"1011EY11EY1102")
     */
    @JsonProperty("bizdept_number")
    private String bizdeptNumber;

    /**
     * 备注
     */
    @JsonProperty("comment")
    private String comment;

    /**
     * 物料明细
     */
    @JsonProperty("billentry")
    private List<OtherOutBillEntry> billentry;
}
