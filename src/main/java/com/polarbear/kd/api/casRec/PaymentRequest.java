package com.polarbear.kd.api.casRec;

import com.polarbear.kd.api.casRec.para.PaymentPara;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 收款单新增保存
 *
 * @apiNote https://dev.kingdee.com/open/detail/api/1758916852244810752
 */
public class PaymentRequest extends KdOpRequest<List<PaymentPara>> {
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/cas_recbill/saveRecBill";
    }

    @Override
    public String logModule() {
        return "kd.cas_recbill.saveRecBill";
    }

    @Override
    public Class<PaymentResponse> getResponseClass() {
        return PaymentResponse.class;
    }

    @Override
    public KdOpRequest<List<PaymentPara>> setLogKey(Config<List<PaymentPara>> config) {
        config.setKey1(o -> {
            String keys = String.join(",", o.stream().map(PaymentPara::getBillNo).toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
