package com.polarbear.kd.api.casRec.para;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 单据体实体类
 */
@Data
public class EntryEntity {
    /**
     * 单据体.id
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 单据体.分录行号
     */
    @JsonProperty("seq")
    private Integer seq;

    /**
     * 单据体.制单人
     */
    @JsonProperty("chgcreateor")
    private String chgCreateor;

    /**
     * 单据体.变更单号
     */
    @JsonProperty("chgno")
    private String chgNo;

    /**
     * 单据体.日期
     */
    @JsonProperty("chgdate")
    private String chgDate;

    /**
     * 单据体.关联单据id
     */
    @JsonProperty("relationid")
    private String relationId;

    /**
     * 单据体.制单人标题
     */
    @JsonProperty("chgcreateortitle")
    private String chgCreateorTitle;

    /**
     * 单据体.变更单号标题
     */
    @JsonProperty("chgnotitle")
    private String chgNoTitle;

    /**
     * 单据体.变更原因标题
     */
    @JsonProperty("chgresontitle")
    private String chgReasonTitle;

    /**
     * 单据体.变更原因子标题
     */
    @JsonProperty("chgresonson")
    private String chgReasonSon;
}