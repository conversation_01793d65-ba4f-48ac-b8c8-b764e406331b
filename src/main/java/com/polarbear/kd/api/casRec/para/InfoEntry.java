package com.polarbear.kd.api.casRec.para;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 流水信息
 */
@Data
public class InfoEntry {
    /**
     * 流水信息.id
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 流水信息.分录行号
     */
    @JsonProperty("seq")
    private Integer seq;

    /**
     * 流水信息.交易明细编号
     */
    @JsonProperty("bei_billno")
    private String beiBillNo;

    /**
     * 流水信息.收款金额
     */
    @JsonProperty("bei_creditamount")
    private BigDecimal beiCreditAmount;

    /**
     * 流水信息.交易流水号
     */
    @JsonProperty("bei_detailid")
    private String beiDetailId;

    /**
     * 流水信息.流水ID
     */
    @JsonProperty("infoid")
    private Long infoId;

    /**
     * 流水信息.票面金额
     */
    @JsonProperty("cdm_amount")
    private BigDecimal cdmAmount;

    /**
     * 流水信息.票据号码
     */
    @JsonProperty("cdm_draftbillno")
    private String cdmDraftBillNo;
}
