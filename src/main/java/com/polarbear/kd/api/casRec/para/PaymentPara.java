package com.polarbear.kd.api.casRec.para;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class PaymentPara {

    /**
     * 单据编号
     */
    @JsonProperty("billno")
    private String billNo;

    /**
     * 业务日期
     */
    @NotNull(message = "业务日期不能为空")
    @JsonProperty("bizdate")
    private LocalDate bizDate;

    /**
     * 付款单位类型：[bos_org：公司，bd_supplier：供应商，bd_customer：客户，bos_user：人员，other：其他，cas_othercontactunit：其他往来单位]
     */
    @NotNull(message = "付款单位类型不能为空")
    @JsonProperty("payertype")
    private String payerType;

    /**
     * 摘要
     */
    @JsonProperty("txt_description")
    private String txtDescription;

    /**
     * 收款金额
     */
    @NotNull(message = "收款金额不能为空")
    @JsonProperty("actrecamt")
    private BigDecimal actRecAmt;

    /**
     * 汇率
     */
    @NotNull(message = "汇率不能为空")
    @JsonProperty("exchangerate")
    private BigDecimal exchangeRate;

    /**
     * 折本位币
     */
    @NotNull(message = "折本位币不能为空")
    @JsonProperty("localamt")
    private BigDecimal localAmt;

    /**
     * 付款单位名称
     */
    @JsonProperty("payername")
    private String payerName;

    /**
     * 付款账户
     */
    @JsonProperty("payeracctbanknum")
    private String payerAcctBankNum;

    /**
     * 结算号
     */
    @JsonProperty("settletnumber")
    private String settleTNumber;

    /**
     * 付款单位ID
     */
    @JsonProperty("payer")
    private Long payer;

    /**
     * 付款单位类型标识ID
     */
    @JsonProperty("payerformid")
    private String payerFormId;

    /**
     * 付款账户类型标识ID
     */
    @JsonProperty("payeraccformid")
    private String payerAccFormId;

    /**
     * 付款账户ID
     */
    @JsonProperty("payeracctbank")
    private Long payerAcctBank;

    /**
     * 业务类型(已废弃)
     */
    @JsonProperty("biztype")
    private String bizType;

    /**
     * 单据类型.id
     */
    @JsonProperty("billtype_id")
    private Long billTypeId;

    /**
     * 单据类型.编码
     */
    @JsonProperty("billtype_number")
    private String billTypeNumber;

    /**
     * 付款银行
     */
    @JsonProperty("payerbankname")
    private String payerBankName;

    /**
     * 代理收款
     */
    @JsonProperty("isagent")
    private Boolean isAgent;

    /**
     * 付款方式：[cash：现购，credit：赊购]
     */
    @JsonProperty("paymentmode")
    private String paymentMode;

    /**
     * 退款退票业务
     */
    @JsonProperty("isrefund")
    private Boolean isRefund;

    /**
     * 全额退款
     */
    @JsonProperty("isfullrefund")
    private Boolean isFullRefund;

    /**
     * 汇率日期
     */
    @JsonProperty("exratedate")
    private LocalDate exRateDate;

    /**
     * 是否操作补充合同号
     */
    @JsonProperty("issupplecontract")
    private Boolean isSuppleContract;

    /**
     * 付款单位类型（多类别基础资料类型）：[bos_org：公司，bd_supplier：供应商，bd_customer：客户，bos_user：人员，cas_othercontactunit：其他往来单位]
     */
    @JsonProperty("itempayertype")
    private String itemPayerType;

    /**
     * 手续费
     */
    @JsonProperty("fee")
    private BigDecimal fee;

    /**
     * 影像编号
     */
    @JsonProperty("imageno")
    private String imageNo;

    /**
     * 实际交易日期
     */
    @JsonProperty("acttradedate")
    private LocalDate actTradeDate;

    /**
     * 付款单位编码
     */
    @JsonProperty("payernumber")
    private String payerNumber;

    /**
     * 是否未认领入账
     */
    @JsonProperty("isunclaim")
    private Boolean isUnclaim;

    /**
     * 红冲标识：[1：被红冲单，2：红冲单]
     */
    @JsonProperty("hotaccount")
    private String hotAccount;

    /**
     * 内部账户币别
     */
    @JsonProperty("inneraccountcurrency")
    private String innerAccountCurrency;

    /**
     * 内部账户收款金额
     */
    @JsonProperty("inneraccountamount")
    private BigDecimal innerAccountAmount;

    /**
     * 换算方式：[0：直接汇率，1：间接汇率]
     */
    @JsonProperty("quotation")
    private String quotation;

    /**
     * 已匹配收款金额
     */
    @JsonProperty("matchamountrec")
    private BigDecimal matchAmountRec;

    /**
     * 未匹配收款金额
     */
    @JsonProperty("unmatchamountrec")
    private BigDecimal unMatchAmountRec;

    /**
     * 匹配标识
     */
    @JsonProperty("matchflag")
    private String matchFlag;

    /**
     * 需关联对方流水
     */
    @JsonProperty("relateotherflow")
    private Boolean relateOtherFlow;

    /**
     * 已匹配付款金额
     */
    @JsonProperty("matchamountpay")
    private BigDecimal matchAmountPay;

    /**
     * 未匹配付款金额
     */
    @JsonProperty("unmatchamountpay")
    private BigDecimal unMatchAmountPay;

    /**
     * 匹配异常原因
     */
    @JsonProperty("matchflagmsg")
    private String matchFlagMsg;

    /**
     * 是否已经使用
     */
    @JsonProperty("isused")
    private Boolean isUsed;

    /**
     * 使用收款的单据
     */
    @JsonProperty("usesourcebillid")
    private String useSourceBillId;

    /**
     * 收款明细
     */
    @JsonProperty("entry")
    private List<Entry> entries;

    /**
     * 单据体列表
     */
    @JsonProperty("entryentity")
    private List<EntryEntity> entryEntities;

    /**
     * 流水信息列表
     */
    @JsonProperty("infoentry")
    private List<InfoEntry> infoEntries;

    /**
     * 结算号基础资料列表
     */
    @JsonProperty("draftbill")
    private List<DraftBill> draftBills;

    /**
     * 收款组织.id
     */
    @JsonProperty("org_id")
    private Long orgId;

    /**
     * 收款组织.编码
     */
    @JsonProperty("org_number")
    private String orgNumber;

    /**
     * 收款银行.id
     */
    @JsonProperty("payeebank_id")
    private Long payeeBankId;

    /**
     * 收款银行.编码
     */
    @JsonProperty("payeebank_number")
    private String payeeBankNumber;

    /**
     * 结算方式.id
     */
    @JsonProperty("settletype_id")
    private Long settleTypeId;

    /**
     * 结算方式.编码
     */
    @JsonProperty("settletype_number")
    private String settleTypeNumber;

    /**
     * 出纳.id
     */
    @JsonProperty("cashier_id")
    private Long cashierId;

    /**
     * 出纳.工号
     */
    @JsonProperty("cashier_number")
    private String cashierNumber;

    /**
     * 付款银行.id
     */
    @JsonProperty("f7_payerbank_id")
    private Long f7PayerBankId;

    /**
     * 付款银行.编码
     */
    @JsonProperty("f7_payerbank_number")
    private String f7PayerBankNumber;

    /**
     * 收款账号
     */
    @JsonProperty("accountcash_id")
    private Long accountCashId;

    /**
     * 收款账号.账户编码
     */
    @JsonProperty("accountcash_number")
    private String accountCashNumber;

    /**
     * 会计.id
     */
    @JsonProperty("clerk_id")
    private Long clerkId;

    /**
     * 会计.工号
     */
    @JsonProperty("clerk_number")
    private String clerkNumber;

    /**
     * 收款账号.id
     */
    @JsonProperty("accountbank_id")
    private Long accountBankId;

    /**
     * 收款账号.编码
     */
    @JsonProperty("accountbank_number")
    private String accountBankNumber;

    /**
     * 收款账号
     */
    @JsonProperty("accountbank_bankaccountnumber")
    private String accountBankBankAccountNumber;

    /**
     * 收款币别.id
     */
    @JsonProperty("currency_id")
    private Long currencyId;

    /**
     * 收款币别.货币代码
     */
    @JsonProperty("currency_number")
    private String currencyNumber;

    /**
     * 本位币.id
     */
    @JsonProperty("basecurrency_id")
    private Long baseCurrencyId;

    /**
     * 本位币.货币代码
     */
    @JsonProperty("basecurrency_number")
    private String baseCurrencyNumber;

    /**
     * 核算组织.id
     */
    @JsonProperty("openorg_id")
    private Long openOrgId;

    /**
     * 核算组织.编码
     */
    @JsonProperty("openorg_number")
    private String openOrgNumber;

    /**
     * 实际付款账户.id
     */
    @JsonProperty("actpayaccount_id")
    private Long actPayAccountId;

    /**
     * 实际付款账户.编码
     */
    @JsonProperty("actpayaccount_number")
    private String actPayAccountNumber;

    /**
     * 汇率表.id
     */
    @JsonProperty("exratetable_id")
    private Long exRateTableId;

    /**
     * 汇率表.编码
     */
    @JsonProperty("exratetable_number")
    private String exRateTableNumber;

    /**
     * 付款单位.id
     */
    @JsonProperty("itempayer_id")
    private Long itemPayerId;

    /**
     * 付款单位.编码
     */
    @JsonProperty("itempayer_number")
    private String itemPayerNumber;

    /**
     * 内部账户账号.id
     */
    @JsonProperty("inneraccount_id")
    private Long innerAccountId;

    /**
     * 内部账户账号.编码
     */
    @JsonProperty("inneraccount_number")
    private String innerAccountNumber;

    /**
     * 源单类型
     * <p>
     *     源单类型：[cas_recbill：收款单，er_repaymentbill：还款单，cas_betransdetail：交易明细，ar_finarbill：财务应收单，cas_paybill：付款单，fca_transdownbill：下拨单，sm_salorder：销售订单，cfm_loanbill：提款单，cdm_drafttradebill：业务处理，ec_incomeapply：建筑请款单，cas_claimcenterbill：认领通知单，bei_intelrec：收款认领中心，bei_tra
     * </p>
     */
    private String sourceBillType;

    /**
     * 收款用途（废弃）.id
     */
    @JsonProperty("receivingtype_id")
    private Long receivingTypeId;

    /**
     * 收款用途（废弃）.编码
     */
    @JsonProperty("receivingtype_number")
    private String receivingTypeNumber;

    /**
     * 来源异构系统
     */
    @JsonProperty("gjwl_srcsystem")
    private String gjwlSrcsystem;

    public PaymentPara() {
        this.gjwlSrcsystem = "A";
    }
}
