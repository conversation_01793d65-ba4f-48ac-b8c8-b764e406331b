package com.polarbear.kd.api.casRec.para;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 单据体实体类
 */
@Data
public class Entry {

    /**
     * id
     */
    @JsonProperty("id")
    private Long id;

    /**
     * id
     */
    @JsonProperty("gjwl_project_number")
    private String gjwlProjectNumber;

    /**
     * 分录行号
     */
    @JsonProperty("seq")
    private Integer seq;

    /**
     * 实收金额
     */
    @JsonProperty("e_actamt")
    private BigDecimal eActAmt;

    /**
     * 实收折本币
     */
    @JsonProperty("e_localamt")
    private BigDecimal eLocalAmt;

    /**
     * 备注
     */
    @JsonProperty("e_remark")
    private String eRemark;

    /**
     * 现金折扣
     */
    @JsonProperty("e_discountamt")
    private BigDecimal eDiscountAmt;

    /**
     * 折扣折本币
     */
    @JsonProperty("e_discountlocamt")
    private BigDecimal eDiscountLocAmt;

    /**
     * 应收金额
     */
    @JsonProperty("e_receivableamt")
    private BigDecimal eReceivableAmt;

    /**
     * 应收折本币
     */
    @JsonProperty("e_receivablelocamt")
    private BigDecimal eReceivableLocAmt;

    /**
     * 已锁定金额
     */
    @JsonProperty("e_lockamt")
    private BigDecimal eLockAmt;

    /**
     * 未锁定金额
     */
    @JsonProperty("e_unlockamt")
    private BigDecimal eUnlockAmt;

    /**
     * 已结算金额
     */
    @JsonProperty("e_settledamt")
    private BigDecimal eSettledAmt;

    /**
     * 未结算金额
     */
    @JsonProperty("e_unsettledamt")
    private BigDecimal eUnsettledAmt;

    /**
     * 核心单据类型
     */
    @JsonProperty("e_corebilltype")
    private String eCoreBillType;

    /**
     * 核心单据号
     */
    @JsonProperty("e_corebillno")
    private String eCoreBillNo;

    /**
     * 核心单据行号
     */
    @JsonProperty("e_corebillentryseq")
    private Integer eCoreBillEntrySeq;

    /**
     * 源单ID
     */
    @JsonProperty("e_sourcebillid")
    private Long eSourceBillId;

    /**
     * 源单分录ID
     */
    @JsonProperty("e_sourcebillentryid")
    private Long eSourceBillEntryId;

    /**
     * 合同号
     */
    @JsonProperty("contractnumber")
    private String contractNumber;

    /**
     * 手续费
     */
    @JsonProperty("e_fee")
    private BigDecimal eFee;

    /**
     * 挂合同批次号
     */
    @JsonProperty("e_contractbatch")
    private String eContractBatch;

    /**
     * 业务日期
     */
    @JsonProperty("entrybizdate")
    private LocalDate entryBizDate;

    /**
     * 已结算金额折本币
     */
    @JsonProperty("e_settledlocalamt")
    private BigDecimal eSettledLocalAmt;

    /**
     * 未结算金额折本位币
     */
    @JsonProperty("e_unsettledlocalamt")
    private BigDecimal eUnsettledLocalAmt;

    /**
     * 已匹配核心单据标识
     */
    @JsonProperty("e_matchselltag")
    private Boolean eMatchSellTag;

    /**
     * 变更拆分分录ID
     */
    @JsonProperty("e_sourcebillresentryid")
    private Long eSourceBillResEntryId;

    /**
     * 合同实体.id
     */
    @JsonProperty("conbillentity_id")
    private String conBillEntityId;

    /**
     * 合同实体.编码
     */
    @JsonProperty("conbillentity_number")
    private String conBillEntityNumber;

    /**
     * 收款用途.id
     */
    @JsonProperty("e_receivingtype_id")
    private Long eReceivingTypeId;

    /**
     * 收款用途.编码
     */
    @JsonProperty("e_receivingtype_number")
    private String eReceivingTypeNumber;

    /**
     * 结算组织.id
     */
    @JsonProperty("e_settleorg_id")
    private Long eSettleOrgId;

    /**
     * 结算组织.编码
     */
    @JsonProperty("e_settleorg_number")
    private String eSettleOrgNumber;

    /**
     * 往来单位类型
     */
    @JsonProperty("e_contactunittype")
    private String eContactUnitType;

    /**
     * 往来单位.id
     */
    @JsonProperty("e_contactunit_id")
    private Long eContactUnitId;

    /**
     * 往来单位.编码
     */
    @JsonProperty("e_contactunit_number")
    private String eContactUnitNumber;

    /**
     * 结算币别.id
     */
    @JsonProperty("e_settlecur_id")
    private Long eSettleCurId;

    /**
     * 结算币别.货币代码
     */
    @JsonProperty("e_settlecur_number")
    private String eSettleCurNumber;

    /**
     * 资金用途.id
     */
    @JsonProperty("e_fundflowitem_id")
    private Long eFundFlowItemId;

    /**
     * 资金用途.编码
     */
    @JsonProperty("e_fundflowitem_number")
    private String eFundFlowItemNumber;

    /**
     * 采购组织.id
     */
    @JsonProperty("purorg_id")
    private Long purOrgId;

    /**
     * 采购组织.编码
     */
    @JsonProperty("purorg_number")
    private String purOrgNumber;

    /**
     * 采购部门.id
     */
    @JsonProperty("purdepartment_id")
    private Long purDepartmentId;

    /**
     * 采购部门.编码
     */
    @JsonProperty("purdepartment_number")
    private String purDepartmentNumber;

    /**
     * 采购组.id
     */
    @JsonProperty("purdept_id")
    private Long purDeptId;

    /**
     * 采购组.编码
     */
    @JsonProperty("purdept_number")
    private String purDeptNumber;

    /**
     * 采购员.id
     */
    @JsonProperty("purchaser_id")
    private Long purchaserId;

    /**
     * 采购员.业务员编码
     */
    @JsonProperty("purchaser_operatornumber")
    private String purchaserOperatorNumber;

    /**
     * 销售组织.id
     */
    @JsonProperty("salesorg_id")
    private Long salesOrgId;

    /**
     * 销售组织.编码
     */
    @JsonProperty("salesorg_number")
    private String salesOrgNumber;

    /**
     * 销售部门.id
     */
    @JsonProperty("salesdept_id")
    private Long salesDeptId;

    /**
     * 销售部门.编码
     */
    @JsonProperty("salesdept_number")
    private String salesDeptNumber;

    /**
     * 销售组.id
     */
    @JsonProperty("salesgroup_id")
    private Long salesGroupId;

    /**
     * 销售组.编码
     */
    @JsonProperty("salesgroup_number")
    private String salesGroupNumber;

    /**
     * 销售员.id
     */
    @JsonProperty("salesman_id")
    private Long salesmanId;

    /**
     * 销售员.业务员编码
     */
    @JsonProperty("salesman_operatornumber")
    private String salesmanOperatorNumber;

    /**
     * 结算汇率
     */
    @JsonProperty("e_settlerate")
    private BigDecimal eSettleRate;
}
