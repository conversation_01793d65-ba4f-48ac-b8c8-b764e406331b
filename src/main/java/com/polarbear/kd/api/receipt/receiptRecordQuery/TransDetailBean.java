package com.polarbear.kd.api.receipt.receiptRecordQuery;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class TransDetailBean {

    /**
     * 收款流水ID，通常作为数据库中的唯一标识符。
     */
    private Long receivablesId;

    /**
     * 收款单号，用于识别特定的收款事务。
     */
    private String receivablesNum;

    /**
     * 收款日期，采用YYYY-MM-DD格式表示。
     */
    private LocalDate receivablesDate;

    /**
     * 收款单位编码，可能是内部使用的代码来标识不同的公司或部门。
     */
    private String compCode;

    /**
     * 收款单位名称，对应于compCode的具体名称。
     */
    private String compName;

    /**
     * 收款账号，银行账户号码。
     */
    private String receivablesBankNum;

    /**
     * 银行流水号，银行交易的唯一标识符。
     */
    private String bankSerialNumber;

    /**
     * 付方单位名称，即付款方的名称。
     */
    private String paymentCompName;

    /**
     * 付款账号，付款方的银行账户号码。
     */
    private String paymentBankNum;

    /**
     * 付款方式，例如现金、支票、电汇等。
     */
    private String paymentTypeName;

    /**
     * 收款金额，使用BigDecimal类型存储货币值，以保证精度。
     */
    private BigDecimal receivablesAmount;

    /**
     * 已认领金额，已经被确认并记录在账目的部分。
     */
    private BigDecimal claimedAmount;

    /**
     * 可认领金额，等待确认的部分。
     */
    private BigDecimal unclaimedAmount;

    /**
     * 备注，用于记录额外的信息或说明。
     */
    private String remark;

    /**
     * 摘要，可能包含交易详情的简短描述。
     */
    private String transAbstr;

    /**
     * 币种编码，标准代码来标识货币种类。
     */
    private String currencyCode;

    /**
     * 币种名称，对应于currencyCode的实际货币名称。
     */
    private String currencyName;

    /**
     * 付款银行，付款方所在的银行名称。
     */
    private String paymentBankName;

    /**
     * 收款银行，收款方所在的银行名称。
     */
    private String receivablesBankName;


}
