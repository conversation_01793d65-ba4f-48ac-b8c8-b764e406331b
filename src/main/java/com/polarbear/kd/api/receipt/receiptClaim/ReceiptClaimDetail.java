package com.polarbear.kd.api.receipt.receiptClaim;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 收款认领结果明细
 * <AUTHOR>
 */
@Data
public class ReceiptClaimDetail {

    /**
     * 认领明细id
     */
    private long receivablesLineId;

    /**
     * 收款单号
     */
    private String receivablesLineNum;

    /**
     * 原收款单号
     */
    private String receivablesNum;

    /**
     * 认领金额
     */
    private BigDecimal claimedAmount;

    /**
     * 业务类型
     */
    private String categoryCode;

    /**
     * 业务类型名称
     */
    private String categoryName;

    /**
     * 客户id
     */
    private long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户编码
     */
    private String customerNumber;

    /**
     * 手续费
     */
    private BigDecimal serviceCharge;

}
