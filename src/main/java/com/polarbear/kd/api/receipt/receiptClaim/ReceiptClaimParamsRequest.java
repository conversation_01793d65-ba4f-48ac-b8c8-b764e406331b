package com.polarbear.kd.api.receipt.receiptClaim;

import com.polarbear.kd.api.EmptyResponse;
import com.polarbear.kd.api.receipt.ReceiptClaimResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

/**
 * 收款流水认领
 * <AUTHOR>
 */
public class ReceiptClaimParamsRequest  extends KdOpRequest<ReceiptClaimParams> {

    public ReceiptClaimParamsRequest(ReceiptClaimParams params)
    {
        this.setParam(params);
    }
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/recClaimInterface/recClaimInterface";
    }

    @Override
    public String logModule() {
        return "kd.bd_receipt.receiptClaim";
    }

    @Override
    public Class<? extends EmptyResponse> getResponseClass() {
        return ReceiptClaimResponse.class;
    }

    @Override
    public boolean standard() {
        return false;
    }


    @Override
    public KdOpRequest<ReceiptClaimParams> setLogKey(Config<ReceiptClaimParams> config) {
        config.setKey1(ReceiptClaimParams::getCompCode);
        config.setKey2(o-> String.valueOf(o.getClaimRevInforBean().getReceivablesId()));
        config.setKey3(o-> o.getClaimRevInforBean().getReceivablesNum());
        return this;
    }

}
