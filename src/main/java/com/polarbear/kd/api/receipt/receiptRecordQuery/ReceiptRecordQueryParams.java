package com.polarbear.kd.api.receipt.receiptRecordQuery;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 收款流水查询(入参)
 * <AUTHOR>
 */
@Data
public class ReceiptRecordQueryParams {


    private QueryPage page;

    /**
     * 公司编码
     */
    private String compCode;

    /**
     * 交易日期起	yyyy-mm-dd
     */
    private String receivablesStartDate;

    /**
     * 交易日期止	yyyy-mm-dd
     */
    private String receivablesEndDate;

    /**
     * 付款单位名称
     */
    private String paymentCompName;
}