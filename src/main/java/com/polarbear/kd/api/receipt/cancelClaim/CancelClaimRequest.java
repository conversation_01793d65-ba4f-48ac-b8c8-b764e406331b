package com.polarbear.kd.api.receipt.cancelClaim;

import com.polarbear.kd.api.EmptyResponse;
import com.polarbear.kd.api.receipt.ReceiptClaimResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;
import org.springframework.util.StringUtils;

/**
 * 取消认领
 * <AUTHOR>
 */
public class CancelClaimRequest  extends KdOpRequest<CancelClaimParams> {

    public CancelClaimRequest(CancelClaimParams params)
    {
        this.setParam(params);
    }
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/cancelRecClaim/calcelrecclaiminterface";
    }

    @Override
    public String logModule() {
        return "kd.bd_receipt.cancelClaim";
    }

    @Override
    public Class<? extends EmptyResponse> getResponseClass() {
        return ReceiptClaimResponse.class;
    }
    @Override
    public boolean standard() {
        return false;
    }

    @Override
    public KdOpRequest<CancelClaimParams> setLogKey(Config<CancelClaimParams> config) {
        config.setKey1(CancelClaimParams::getCompCode);
        config.setKey2(o-> String.valueOf(o.getReceivablesId()));
        config.setKey3(CancelClaimParams::getRemark);
        config.setKey4(CancelClaimParams::getCasRecBillId);
        return this;
    }

}
