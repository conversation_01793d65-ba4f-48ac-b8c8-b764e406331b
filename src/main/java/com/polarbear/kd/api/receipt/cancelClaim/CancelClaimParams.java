package com.polarbear.kd.api.receipt.cancelClaim;
import lombok.Data;

/**
 * 取消认领（入参）
 * <AUTHOR>
 */
@Data
public class CancelClaimParams {

    /**
     * 公司编码
     */
    private String compCode;

    /**
     * 操作类型 UNCLAIM取消认领 AUDIT认领复核
     */
    private String operateType;

    /**
     * 收款流水id
     */
    private Long receivablesId;

    /**
     * 操作原因
     */
    private String remark;

    /**
     *  金蝶收款单据id
     */
    private String casRecBillId;
}
