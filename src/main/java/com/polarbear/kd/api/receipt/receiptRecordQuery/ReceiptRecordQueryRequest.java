package com.polarbear.kd.api.receipt.receiptRecordQuery;
import com.polarbear.kd.api.EmptyResponse;
import com.polarbear.kd.api.receipt.receiptClaim.ReceiptClaimParams;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

/**
 * 收款流水查询
 * <AUTHOR>
 */
public class ReceiptRecordQueryRequest extends KdOpRequest<ReceiptRecordQueryParams> {


    public ReceiptRecordQueryRequest(ReceiptRecordQueryParams params)
    {
        this.setParam(params);
    }
    @Override
    public String getUrlPath() {
        return "/v2/gjwl/cas/transDetail/transDetailNewQuery";
    }

    @Override
    public String logModule() {
        return "kd.bd_receipt.receiptRecordQuery";
    }

    @Override
    public Class<? extends EmptyResponse> getResponseClass() {
        return DetailQueryResponse.class;
    }

    @Override
    public boolean standard() {
        return false;
    }


    @Override
    public KdOpRequest<ReceiptRecordQueryParams> setLogKey(Config<ReceiptRecordQueryParams> config) {
        config.setKey1(ReceiptRecordQueryParams::getCompCode);
        return this;
    }


}
