package com.polarbear.kd.enums;

import lombok.Getter;

@Getter
public enum KingdeePushEnum {
  SALE_ORDER_PUSH_SAL_OUT_BILL(
      1, "sm_salorder", "im_saloutbill", "610056021305574400", "销售订单下推销售出库单"),
  SALE_OUT_BILL_PUSH_SAL_RETURN_BILL(
      2, "im_saloutbill", "im_saloutbill", "705622291240812544", "销售出库单下推销售退货单"),
  PURCHASE_ORDER_PUSH_PUR_IN_BILL(
      3, "pm_purorderbill", "im_purinbill", "565033700123759616", "采购订单下推采购入库单"),
  PURCHASE_IN_BILL_PUSH_PUR_RETURN_BILL(
      4, "im_purinbill", "im_purinbill", "705591916980436992", "采购入库单下推采购退料单"),
  TRANS_OUT_BILL_PUSH_TRANS_IN_BILL(
      5, "im_transoutbill", "im_transinbill", "475190152356975616", "分步调出单下推分步调入单"),
  ;

  private final int index;
  private final String source;
  private final String target;
  private final String rule;
  private final String description;

  KingdeePushEnum(int index, String source, String target, String rule, String description) {
    this.index = index;
    this.source = source;
    this.target = target;
    this.rule = rule;
    this.description = description;
  }

  public static KingdeePushEnum fromIndex(int index) {
    for (KingdeePushEnum pushEnum : KingdeePushEnum.values()) {
      if (pushEnum.getIndex() == index) {
        return pushEnum;
      }
    }
    return null;
  }
}
