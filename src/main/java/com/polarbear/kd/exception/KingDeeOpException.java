package com.polarbear.kd.exception;

import com.polarbear.springframework.boot.service.exception.ServiceException;

/**
 * 操作异常
 *
 * <AUTHOR>
 * @date 2023/10/18
 */
public class KingDeeOpException extends ServiceException {
    private Code code;

    public KingDeeOpException(Code code) {
        super(code.message);
        this.code = code;
    }

    public KingDeeOpException(Code code, String info) {
        super(code.code, String.format("code.message: %s, info: %s", code.message, info));
        this.code = code;
    }

    public KingDeeOpException(String errorCode, String message) {
        super(errorCode, String.format("code.message: %s", message));
    }

    public KingDeeOpException(Code code, Exception e) {
        super(code.code, e);
        this.code = code;
    }

    public KingDeeOpException(Exception e) {
        super(Code.UNRECOGNIZED_EXCEPTION.toString(), e);
        this.code = Code.UNRECOGNIZED_EXCEPTION;
    }

    public static enum Code {
        UNRECOGNIZED_EXCEPTION("999", "未预知异常"),
        HTTP_RESPONSE_STATUS_CODE_NOT_2XX("10001", "HTTP请求返回状态码非200"),
        INIT_REQUEST_PARAM_ERROR("10002", "初始化Request失败"),
        HTTP_REQUEST_ERROR("10004", "发送http请求失败"),
        JSON_ERROR("10005", "json序列化（反序列化）失败"),
        UNSUPPORTED_HTTP_METHOD("10006", "不支持的http方法"),
        PARAMETER_REQUIRED_CHECK_ERROR("20001", "必填参数校验失败"),
        TOKEN_CHECK_SIGN_ERROR("401", "未授权访问,消息网关校验签名异常"),
        PARAMETER_CHECK_ERROR("400", "参数校验失败"),
        REQUEST_LIMITS_ERROR("429", "禁止访问（IP限制等）"),
        Forbidden_ERROR("403", "禁止访问（IP限制等）"),
        API_ERROR("404", "API不存在"),
        DATA_DUPLICATE_ERROR("601", "数据重复"),
        NOT_FOUND_DATA_ERROR("602", "找不到数据"),
        DATA_INVALID_ERROR("603", "数据无效"),
        UPDATE_DATA_FAIL_ERROR("611", "更新数据失败"),
        NO_DATA_OPERATION_PERMISSION_ERROR("612", "没有数据操作权限"),
        SCRIPT_EXECUTE_ERROR("701", "脚本执行错误"),
        PLUGIN_EXECUTE_ERROR("702", "插件执行时发生异常"),
        INIT_REQUEST_CONFIG_ERROR("20004", "配置信息缺失"),
        MATERIAL_GATEWAY_RESP_EMPTY_ERROR("30001", "返回body为空"),
        DOWN_FILE_ERROR("30002", "下载文件失败");
        final String code;
        final String message;

        private Code(String code, String message) {
            this.code = code;
            this.message = message;
        }
    }
}
