package com.polarbear.kd.core;

import com.polarbear.springframework.boot.service.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class Register {
    private static final Map<String, Config<?>> configMap = new HashMap<>();
    private static final Register instance = new Register();

    public Register getInstance() {
        return instance;
    }

    public static <T> void register(Config<T> config) {
        if (config == null) {
            throw new NullPointerException("配置不能为空");
        }
        String name = config.getModule();
        if (StringUtils.isBlank(name)) {
            throw new ServiceException("E21014", "注册失败,未设置日志模块");
        }
        if (configMap.containsKey(name)) {
            return;
        }
        configMap.put(name, config);
    }

    public static <T> void register(Supplier<Config<T>> config) {
        if (config == null) {
            throw new NullPointerException("配置不能为空");
        }
        register(config.get());
    }

    public static <T> ConfigBuilder<T> getConfigBuilder(String name) {
        if (!configMap.containsKey(name)) {
            return null;
        }
        return new ConfigBuilder<>(name);
    }

    public static class ConfigBuilder<T> {
        private final Config<T> config;

        @SuppressWarnings("unchecked")
        public ConfigBuilder(String name) {
            if (!configMap.containsKey(name)) {
                throw new ServiceException("E21014", "未注册的配置:" + name);
            }
            try {
                config = (Config<T>) configMap.get(name);
            } catch (ClassCastException e) {
                throw new ServiceException("E21015", String.format("当前模块%s注册请求参数和实际请求参数不一致,日志记录失败", name));
            }
        }

        public String getKey1(T t) {
            return config.getKey1().apply(t);
        }

        public String getKey2(T t) {
            return config.getKey2().apply(t);
        }

        public String getKey3(T t) {
            return config.getKey3().apply(t);
        }

        public String getKey4(T t) {
            return config.getKey4().apply(t);
        }

        public String getModule() {
            return config.getModule();
        }
    }
}
