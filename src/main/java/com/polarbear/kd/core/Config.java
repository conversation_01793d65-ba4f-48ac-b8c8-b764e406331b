package com.polarbear.kd.core;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.function.Function;

@Data
@Accessors(chain = true)
public class Config<T> {
    String module;

    Function<? super T, String> key1;
    Function<? super T, String> key2;
    Function<? super T, String> key3;
    Function<? super T, String> key4;

    public Config() {
        this.key1 = t -> null;
        this.key2 = t -> null;
        this.key3 = t -> null;
        this.key4 = t -> null;
    }
}
