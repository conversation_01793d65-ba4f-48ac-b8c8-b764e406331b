package com.polarbear.kd.core;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarbear.base.api.model.SdkLogDTO;
import com.polarbear.kd.exception.KingDeeOpException;
import com.polarbear.springframework.boot.common.model.CommonResult;
import com.polarbear.springframework.boot.service.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class Log4Sdk {
    public static SdkLogBuilder builder(ObjectMapper json) {
        return new SdkLogBuilder(json);
    }

    public static class SdkLogBuilder {
        private final SdkLog log = new SdkLog();
        private final ObjectMapper json;

        public SdkLogBuilder(ObjectMapper json) {
            this.json = json;
            log.setRequestTime(LocalDateTime.now()).setCreateTime(LocalDateTime.now()).setStatus(0);
        }

        public SdkLogBuilder module(String module) {
            log.setModule(module);
            return this;
        }

        public <T> SdkLogBuilder request(T t) {
            if (t != null) {
                if (StringUtils.isBlank(log.getModule())) {
                    throw new ServiceException("E21014", "获取日志模块失败,日志模块未设置或把设置方法调整到当前方法之前设置");
                }
                Register.ConfigBuilder<T> configBuilder = Register.getConfigBuilder(log.getModule());
                if (configBuilder != null) {
                    try {
                        log.setKey1(configBuilder.getKey1(t)).setKey2(configBuilder.getKey2(t))
                                .setKey3(configBuilder.getKey3(t)).setKey4(configBuilder.getKey4(t));
                    } catch (Exception e) {
                        throw new ServiceException("E21015", "设置日志关键key失败", e.getCause());
                    }
                }
                try {
                    setValue(t, log::setRequestParam);
                } catch (JsonProcessingException e) {
                    log.setErrorCode("E21010").setErrorMessage("记录请求数据异常。" + e.getMessage()).setStatus(1);
                } catch (Exception e) {
                    log.setErrorCode("E21011").setErrorMessage("记录请求数据异常。" + e.getMessage()).setStatus(1);
                }
            }
            return this;
        }

        /*
         * 直接设置最终请求内容
         */
        public SdkLogBuilder requestContent(String content) throws JsonProcessingException {
            setValue(content, log::setRequestParam);
            return this;
        }

        public <T> SdkLogBuilder response(T t) {
            try {
                log.setResponseTime(LocalDateTime.now());
                if (t instanceof CommonResult<?>) {
                    CommonResult<?> result = (CommonResult<?>) t;
                    if (!result.isSuccess()) {
                        log.setErrorCode(result.getErrorCode()).setErrorMessage(result.getErrorMessage()).setStatus(1);
                    }
                }
                setValue(t, log::setResponseParam);
            } catch (JsonProcessingException e) {
                log.setErrorCode("E21012").setErrorMessage("记录响应数据异常。" + e.getMessage()).setStatus(1);
            } catch (Exception e) {
                log.setErrorCode("E21013").setErrorMessage("记录响应数据异常。" + e.getMessage()).setStatus(1);
            }
            return this;
        }

        private <T> void setValue(T t, Consumer<String> setKey) throws JsonProcessingException {
            if (t instanceof String) {
                setKey.accept((String) t);
            } else if (t instanceof Number || t instanceof Boolean) {
                setKey.accept(t.toString());
            } else if (t instanceof LocalDateTime) {
                setKey.accept(((LocalDateTime) t).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else if (t instanceof LocalDate) {
                setKey.accept(((LocalDate) t).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            } else if (t instanceof LocalTime) {
                setKey.accept(((LocalTime) t).format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            } else {
                String s = json.writeValueAsString(t);
                setKey.accept(s);
            }
        }

        public SdkLogBuilder error(ServiceException e) {
            log.setErrorCode(e.getErrorCode()).setErrorMessage(e.getMessage()).setStatus(1)
                    .setErrorStack(getStackTrace(e.getStackTrace()));
            return this;
        }

        private static String getStackTrace(StackTraceElement[] e) {
            //.filter(o -> o.getClassName().contains("com.polarbear"))
            return Arrays.stream(e).map(o -> "\nat " + o).collect(Collectors.joining());
        }

        public SdkLogBuilder error(Exception e) {
            log.setErrorMessage(e.getMessage()).setStatus(1).setErrorStack(getStackTrace(e.getStackTrace()));
            if (e.getCause() != null) {
                error(e.getCause());
            }
            if (e instanceof KingDeeOpException) {
                log.setErrorCode(((KingDeeOpException) e).getErrorCode());
            }
            return this;
        }

        public SdkLogBuilder error(Throwable e) {
            log.setErrorMessage(e.getMessage()).setStatus(1).setErrorStack(getStackTrace(e.getStackTrace()));
            return this;
        }

        public SdkLogBuilder error(String errorMessage, String errorCode) {
            log.setErrorMessage(errorMessage).setErrorCode(errorCode).setStatus(1);
            return this;
        }

        public SdkLogDTO build() {
            if (StringUtils.isBlank(log.getModule())) {
                throw new ServiceException("E21014", "请设置日志模块");
            }
            //异常情况没有响应时间,统一设置响应时间
            if (log.getResponseTime() == null) {
                log.setResponseTime(LocalDateTime.now());
            }
            SdkLogDTO logger = new SdkLogDTO();
            logger.setErrorCode(log.getErrorCode());
            logger.setErrorMessage(log.getErrorMessage());
            logger.setErrorStack(log.getErrorStack());
            logger.setId(log.getId());
            logger.setKey1(log.getKey1());
            logger.setKey2(log.getKey2());
            logger.setKey3(log.getKey3());
            logger.setKey4(log.getKey4());
            logger.setModule(log.getModule());
            logger.setRequestParam(log.getRequestParam());
            logger.setResponseParam(log.getResponseParam());
            logger.setRequestTime(log.getRequestTime());
            logger.setResponseTime(log.getResponseTime());
            logger.setStatus(log.getStatus());
            return logger;
        }
    }
}
