package com.polarbear.kd.core;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.polarbear.base.api.client.KdClinet;
import com.polarbear.base.api.client.SdkLogClient;
import com.polarbear.base.api.model.KdAccessTokenDTO;
import com.polarbear.base.api.model.SdkLogDTO;
import com.polarbear.kd.api.EmptyResponse;
import com.polarbear.kd.exception.KingDeeOpException;
import com.polarbear.springframework.boot.common.model.CommonResult;
import com.polarbear.springframework.boot.service.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
public class KingdeeDefaultOpClient {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(180, TimeUnit.SECONDS)
            .readTimeout(180, TimeUnit.SECONDS)
            .writeTimeout(180, TimeUnit.SECONDS)
            .build();
    private static KingdeeDefaultOpClient instance = null;

    private KingdeeDefaultOpClient() {

    }

    public static KingdeeDefaultOpClient instance() {
        if (instance == null) {
            instance = new KingdeeDefaultOpClient();
        }
        return instance;
    }

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(SerializationFeature.INDENT_OUTPUT, true)
                .configure(SerializationFeature.FAIL_ON_UNWRAPPED_TYPE_IDENTIFIERS, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false);

        JavaTimeModule module = new JavaTimeModule();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTimeDeserializer dateTimeDeserializer = new LocalDateTimeDeserializer(dateTimeFormatter);
        LocalDateTimeSerializer dateTimeSerializer = new LocalDateTimeSerializer(dateTimeFormatter);
        module.addDeserializer(LocalDateTime.class, dateTimeDeserializer);
        module.addSerializer(LocalDateTime.class, dateTimeSerializer);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateDeserializer dateDeserializer = new LocalDateDeserializer(dateFormatter);
        LocalDateSerializer dateSerializer = new LocalDateSerializer(dateFormatter);
        module.addDeserializer(LocalDate.class, dateDeserializer);
        module.addSerializer(LocalDate.class, dateSerializer);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DateSerializer simpleDateSerializer = new DateSerializer(true, simpleDateFormat);
        DateDeserializers.DateDeserializer simpleDateDeserializer = new DateDeserializers.DateDeserializer();
        module.addDeserializer(Date.class, simpleDateDeserializer);
        module.addSerializer(Date.class, simpleDateSerializer);
        objectMapper.registerModules(module);
    }

    public <T> String requestContent(String url, String logModule, T requestParam, KdClinet api, SdkLogClient sdkLogClient) {
        CommonResult<KdAccessTokenDTO> accessTokenResult = api.getAccessToken("");
        if (!accessTokenResult.isSuccess()) {
            throw new KingDeeOpException(KingDeeOpException.Code.TOKEN_CHECK_SIGN_ERROR, accessTokenResult.getErrorMessage());
        }
        return requestContent(url, logModule, requestParam, accessTokenResult.getData(), sdkLogClient);
    }

    public static class RequestData<T> {
        private final T data;

        public RequestData(T data) {
            this.data = data;
        }

        public T getData() {
            return data;
        }
    }


    public <T> String requestContent(String url, String logModule, T requestParam, KdAccessTokenDTO accessToken, SdkLogClient sdkLogClient) {
        return requestContent(url, logModule, null, requestParam, accessToken, sdkLogClient);
    }

    public <T> String requestContent(String url, String logModule, String logKey, T requestParam, KdAccessTokenDTO accessToken, SdkLogClient sdkLogClient) {
        if (accessToken == null) {
            throw new ServiceException("accessToken is null");
        }
        if (!accessToken.getNeedCall()) {
            //The tenant does not use KingDee system,so is not request api.
            return null;
        }
        if (requestParam == null) {
            throw new ServiceException("requestParam is null");
        }
        if (url == null) {
            throw new ServiceException("301", "url is null");
        }

        Log4Sdk.SdkLogBuilder logBuilder = Log4Sdk.builder(objectMapper).module(logModule == null ? url : logModule)
                .request(requestParam instanceof RequestData ? ((RequestData<?>) requestParam).getData() : requestParam);
        try {
            if (accessToken.getAccessToken() == null) {
                throw new KingDeeOpException(KingDeeOpException.Code.TOKEN_CHECK_SIGN_ERROR, "can not get accessToken, please check your access token or refresh token");
            }
            String host = accessToken.getServerUrl();
            if (host.lastIndexOf("/") == host.length() - 1) {
                host = host.substring(0, host.length() - 1);
            }
            try {
                String requestContent = objectMapper.writeValueAsString(requestParam);
                logBuilder.requestContent(requestContent);
                RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), requestContent);
                Request request = new Request.Builder()
                        .url(host + "/kapi" + url)
                        .header("accesstoken", accessToken.getAccessToken())
                        .header("Content-Type", "application/json")
                        .header("x-acgw-identity", accessToken.getXAcgwIdentity())
                        .header("Idempotency-Key", UUID.randomUUID().toString())
                        .post(requestBody)
                        .build();
                try (okhttp3.Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        switch (response.code()) {
                            case 999:
                                throw new KingDeeOpException(KingDeeOpException.Code.UNRECOGNIZED_EXCEPTION, response.message());
                            case 400:
                                throw new KingDeeOpException(KingDeeOpException.Code.PARAMETER_CHECK_ERROR, response.message());
                            case 401:
                                throw new KingDeeOpException(KingDeeOpException.Code.TOKEN_CHECK_SIGN_ERROR, response.message());
                            case 403:
                                throw new KingDeeOpException(KingDeeOpException.Code.Forbidden_ERROR, response.message());
                            case 404:
                                throw new KingDeeOpException(KingDeeOpException.Code.API_ERROR, response.message());
                            case 601:
                                throw new KingDeeOpException(KingDeeOpException.Code.DATA_DUPLICATE_ERROR, response.message());
                        }
                        throw new ServiceException(response.code() + "", response.message());
                    }
                    String responseContent = response.body() == null ? "{}" : response.body().string();
                    logBuilder.response(responseContent);
                    return responseContent;
                }
            } catch (JsonProcessingException e) {
                throw new KingDeeOpException(KingDeeOpException.Code.JSON_ERROR, e);
            } catch (IOException e) {
                throw new KingDeeOpException(KingDeeOpException.Code.HTTP_REQUEST_ERROR, e);
            }
        } catch (Exception ex) {
            logBuilder.error(ex);
            throw ex;
        } finally {
            try {
                SdkLogDTO log = logBuilder.build();
                if (logKey != null) {
                    log.setKey1(logKey);
                }
                if (sdkLogClient != null) {
                    sdkLogClient.saveLog(log);
                }
            } catch (Exception e) {
                log.error("保存金蝶请求日志异常", e);
            }
        }
    }

    public <T, R> R request(String url, String logModule, T requestParam, KdClinet api, SdkLogClient sdkLogClient, Class<R> clazz) {
        return response(requestContent(url, logModule, requestParam, api, sdkLogClient), clazz);
    }

    public <T, R> R request(String url, String logModule, T requestParam, KdAccessTokenDTO accessToken, SdkLogClient sdkLogClient, Class<R> clazz) {
        return response(requestContent(url, logModule, requestParam, accessToken, sdkLogClient), clazz);
    }

    @SuppressWarnings("unchecked")
    public <T, R> KdResponseWrapper<R> request(KdOpRequest<T> request, KdAccessTokenDTO accessToken, boolean standard) {
        String responseContent = requestContent(request.getUrlPath(), request.logModule(), standard ? new RequestData<>(request.getParam()) : request.getParam(), accessToken, request.getSdkLogClient());
        if (responseContent == null) {
            return null;
        }
        try {
            EmptyResponse response = response(responseContent, request.getResponseClass());
            response.setOriginalResponse(responseContent);
            KdResponseWrapper<R> wrapper = new KdResponseWrapper<>();
            wrapper.setResponse((R) response);
            return wrapper;
        } catch (Exception e) {
            throw new KingDeeOpException(KingDeeOpException.Code.JSON_ERROR, e);
        }
    }

    public <R> R response(String responseContent, Class<R> clazz) {
        if (responseContent == null) {
            return null;
        }
        R response;
        try {
            response = objectMapper.readValue(responseContent, clazz);
        } catch (Exception e) {
            throw new KingDeeOpException(KingDeeOpException.Code.JSON_ERROR, e);
        }
        return response;
    }

}
