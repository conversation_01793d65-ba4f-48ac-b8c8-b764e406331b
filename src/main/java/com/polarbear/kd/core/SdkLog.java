package com.polarbear.kd.core;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * (SdkLog)表BO
 *
 * <AUTHOR>
 * @since 2024-09-24 15:57:22
 */
@Data
@Accessors(chain = true)
public class SdkLog {
    /**
     * 主键
     */
    private String id;
    /**
     * 请求参数
     */
    private String requestParam;
    /**
     * 返回参数
     */
    private String responseParam;
    /**
     * 自定义key1
     */
    private String key1;
    /**
     * 自定义key2
     */
    private String key2;
    /**
     * 自定义key3
     */
    private String key3;
    /**
     * 自定义key4
     */
    private String key4;
    /**
     * 模块
     */
    private String module;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误堆栈
     */
    private String errorStack;
    /**
     * 状态 0-请求成功 1-请求失败
     */
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    /**
     * 请求时间
     */
    private LocalDateTime requestTime;
    /**
     * 响应时间
     */
    private LocalDateTime responseTime;
}

