package com.polarbear.kd;

import com.polarbear.kd.api.project.Project;
import com.polarbear.kd.api.project.ProjectSaveRequest;
import com.polarbear.kd.api.project.ProjectSaveResponse;
import com.polarbear.kd.api.project.ProjectUnAudit;
import com.polarbear.kd.api.project.ProjectUnAuditRequest;
import com.polarbear.kd.api.project.ProjectUnAuditResponse;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目保存接口测试
 * 
 * <AUTHOR>
 */
public class ProjectSaveTest extends BasicTest {

    @Test
    public void testSaveProject() {
        // 创建项目保存请求
        ProjectSaveRequest request = new ProjectSaveRequest();
        
        // 创建项目信息列表
        List<Project> projects = new ArrayList<>();
        Project project = new Project();
        project.setNumber("Prj-000001");
        project.setName("测试项目01");
        project.setCreateorgNumber("gjwl");
        project.setGjwlAppid("iIa9l");
        project.setGjwlSourcesystemtype("广交云供应链管理系统");
        
        projects.add(project);
        request.setParam(projects);
        
        // 验证请求参数设置
        assert request.getParam() != null;
        assert request.getParam().size() == 1;
        assert "Prj-000001".equals(request.getParam().get(0).getNumber());
        assert "测试项目01".equals(request.getParam().get(0).getName());
        
        // 验证接口配置
        assert "/v2/gjwl/basedata/bd_project/saveProject".equals(request.getUrlPath());
        assert "kd.bd_project.saveProject".equals(request.logModule());
        assert ProjectSaveResponse.class.equals(request.getResponseClass());
        
        System.out.println("项目保存接口测试通过");
    }

    @Test
    public void testUpdateProject() {
        // 创建项目修改请求
        ProjectSaveRequest request = new ProjectSaveRequest();
        
        // 创建项目信息列表（修改时需要传入ID）
        List<Project> projects = new ArrayList<>();
        Project project = new Project();
        project.setId("2274786458764061696"); // 修改时需传入项目ID
        project.setNumber("Prj-000001");
        project.setName("修改后的项目名称");
        project.setCreateorgNumber("gjwl");
        project.setGjwlAppid("iIa9l");
        project.setGjwlSourcesystemtype("广交云供应链管理系统");
        
        projects.add(project);
        request.setParam(projects);
        
        // 验证修改请求参数
        assert request.getParam() != null;
        assert request.getParam().size() == 1;
        assert "2274786458764061696".equals(request.getParam().get(0).getId());
        assert "修改后的项目名称".equals(request.getParam().get(0).getName());
        
        System.out.println("项目修改接口测试通过");
    }

    @Test
    public void testBatchSaveProjects() {
        // 创建批量项目保存请求
        ProjectSaveRequest request = new ProjectSaveRequest();
        
        // 创建多个项目信息
        List<Project> projects = new ArrayList<>();
        
        // 第一个项目
        Project project1 = new Project();
        project1.setNumber("Prj-000001");
        project1.setName("测试项目01");
        project1.setCreateorgNumber("gjwl");
        project1.setGjwlAppid("iIa9l");
        project1.setGjwlSourcesystemtype("广交云供应链管理系统");
        projects.add(project1);
        
        // 第二个项目
        Project project2 = new Project();
        project2.setNumber("Prj-000002");
        project2.setName("测试项目02");
        project2.setCreateorgNumber("gjwl");
        project2.setGjwlAppid("iIa9m");
        project2.setGjwlSourcesystemtype("广交云供应链管理系统");
        projects.add(project2);
        
        request.setParam(projects);
        
        // 验证批量请求参数
        assert request.getParam() != null;
        assert request.getParam().size() == 2;
        assert "Prj-000001".equals(request.getParam().get(0).getNumber());
        assert "Prj-000002".equals(request.getParam().get(1).getNumber());
        
        System.out.println("批量项目保存接口测试通过");
    }

    @Test
    public void testProjectValidation() {
        // 测试必填字段验证
        Project project = new Project();
        
        // 验证必填字段
        project.setNumber("Prj-000001");
        assert "Prj-000001".equals(project.getNumber());
        
        project.setName("测试项目");
        assert "测试项目".equals(project.getName());
        
        project.setCreateorgNumber("gjwl");
        assert "gjwl".equals(project.getCreateorgNumber());
        
        project.setGjwlAppid("testAppId");
        assert "testAppId".equals(project.getGjwlAppid());
        
        project.setGjwlSourcesystemtype("广交云供应链管理系统");
        assert "广交云供应链管理系统".equals(project.getGjwlSourcesystemtype());
        
        // 验证可选字段
        project.setId("testId");
        assert "testId".equals(project.getId());
        
        System.out.println("项目字段验证测试通过");
    }

    @Test
    public void testUnAuditProject() {
        // 创建项目反审核请求
        ProjectUnAuditRequest request = new ProjectUnAuditRequest();

        // 创建项目反审核参数
        ProjectUnAudit unAudit = new ProjectUnAudit();
        List<String> projectIds = new ArrayList<>();
        projectIds.add("1402088445405483008");
        projectIds.add("5764822583698205696");
        unAudit.setId(projectIds);

        request.setParam(unAudit);

        // 验证请求参数设置
        assert request.getParam() != null;
        assert request.getParam().getId() != null;
        assert request.getParam().getId().size() == 2;
        assert "1402088445405483008".equals(request.getParam().getId().get(0));
        assert "5764822583698205696".equals(request.getParam().getId().get(1));

        // 验证接口配置
        assert "/v2/basedata/bd_project/batchUnaudit".equals(request.getUrlPath());
        assert "kd.bd_project.unAuditProject".equals(request.logModule());
        assert ProjectUnAuditResponse.class.equals(request.getResponseClass());

        System.out.println("项目反审核接口测试通过");
    }

    @Test
    public void testUnAuditSingleProject() {
        // 创建单个项目反审核请求
        ProjectUnAuditRequest request = new ProjectUnAuditRequest();

        // 创建单个项目反审核参数
        ProjectUnAudit unAudit = new ProjectUnAudit();
        List<String> projectIds = new ArrayList<>();
        projectIds.add("2274786458764061696");
        unAudit.setId(projectIds);

        request.setParam(unAudit);

        // 验证单个项目反审核请求参数
        assert request.getParam() != null;
        assert request.getParam().getId() != null;
        assert request.getParam().getId().size() == 1;
        assert "2274786458764061696".equals(request.getParam().getId().get(0));

        System.out.println("单个项目反审核接口测试通过");
    }

    @Test
    public void testProjectUnAuditValidation() {
        // 测试项目反审核参数验证
        ProjectUnAudit unAudit = new ProjectUnAudit();

        // 验证ID列表设置
        List<String> projectIds = new ArrayList<>();
        projectIds.add("testId1");
        projectIds.add("testId2");
        projectIds.add("testId3");

        unAudit.setId(projectIds);
        assert unAudit.getId() != null;
        assert unAudit.getId().size() == 3;
        assert "testId1".equals(unAudit.getId().get(0));
        assert "testId2".equals(unAudit.getId().get(1));
        assert "testId3".equals(unAudit.getId().get(2));

        System.out.println("项目反审核参数验证测试通过");
    }
}
