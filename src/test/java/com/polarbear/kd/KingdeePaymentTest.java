package com.polarbear.kd;

import com.polarbear.base.api.model.KdAccessTokenDTO;
import com.polarbear.kd.api.SimpleResponse;
import com.polarbear.kd.api.casRec.PaymentRequest;
import com.polarbear.kd.api.casRec.para.Entry;
import com.polarbear.kd.api.casRec.para.PaymentPara;
import com.polarbear.kd.api.receipt.ReceiptClaimResponse;
import com.polarbear.kd.api.receipt.cancelClaim.CancelClaimParams;
import com.polarbear.kd.api.receipt.cancelClaim.CancelClaimRequest;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;


public class KingdeePaymentTest {


    @Test
    public void save() {
        PaymentRequest paymentRequest = new PaymentRequest();
        List<PaymentPara> paramList = paymentRequest.getParam();
        PaymentPara param = new PaymentPara();
        param.setBillNo("45016");
        param.setBizDate(LocalDate.parse("2024-09-13"));
        param.setPayerType("bd_customer");
        param.setTxtDescription("");
        param.setActRecAmt(BigDecimal.valueOf(150));
        param.setExchangeRate(BigDecimal.valueOf(1));
        param.setLocalAmt(BigDecimal.valueOf(150));
        param.setPayerName("测试数据勿动!608客户TX");

        List<Entry> entries = new ArrayList<>();
        Entry entry = new Entry();

        entry.setEActAmt(BigDecimal.valueOf(150));
        entry.setELocalAmt(BigDecimal.valueOf(150));
        entry.setESettledAmt(BigDecimal.valueOf(150));
        entry.setEUnsettledAmt(BigDecimal.valueOf(0));
        entry.setERemark("核销人：gjzy");
        entry.setECoreBillNo("45016");
        entries.add(entry);
        param.setEntries(entries);


        KdAccessTokenDTO accessToken = new KdAccessTokenDTO() {{
            setNeedCall(true);
            setAccessToken("OPENAPIAUTH_MjA1OTYyMDU5MTY4NTY5OTU4NF84SHE3SkpQUVhGSlNKOGg3UXU1eTFVbzRidFdzaWZZVUUzbktRaTFVMG5ZalRsQ2hUSjJEQ1FqNDJRSFlYNk42VlFEV3BKTnpZWmRjM01OZUt6YTNPYWRGRUxsb3d2QnFCYzFC");
            setXAcgwIdentity("djF8MTkyOGIzZmYzZTIwMzgyYzIzMDF8NDg4MjUxMzA3Nzc5OXxFC68LZ4d-8Se3YvNKAhxG5E6W4P7M0yRbko5Eg4K-X3w=");
            setServerUrl("https://gjwl.test.kdgalaxy.com");
            setClientId("GFSystemIterface");
            setClientSecret("Aa12345678900000!");
            setUsername("yaojinkang");
            setAccountId("2059620591685699584");
        }};

        paramList.add(param);
        SimpleResponse request = paymentRequest.execute(accessToken);
    }


    @Test
    public void save3() {
        CancelClaimParams params = new CancelClaimParams() {{
            setCompCode("223223");
            setReceivablesId(Long.valueOf("*********"));
            setRemark("取消收款单");
            setOperateType("UNCLAIM");
        }};
        CancelClaimRequest cancelClaimRequest = new CancelClaimRequest(params);


        KdAccessTokenDTO accessToken = new KdAccessTokenDTO() {{
            setNeedCall(true);
            setAccessToken("OPENAPIAUTH_MjA1OTYyMDU5MTY4NTY5OTU4NF9uSEt5cE9lamIyUVAyMkdqbzhyUkZ3S0lqNTBxQmF2dk02NDJRN2U4V1hvaWhtcGVTN2hURmdFYVZuM0lGT3A3SDIxM3dGRWRRV2pEcUwybVhYcWpaZzNKdGhZaEZpMUhJVTJu");
            setXAcgwIdentity("djF8MTkyOGIzZmYzZTIwMzgyYzIzMDF8NDg4MjUxMzA3Nzc5OXxFC68LZ4d-8Se3YvNKAhxG5E6W4P7M0yRbko5Eg4K-X3w=");
            setServerUrl("https://gjwl.test.kdgalaxy.com");
            setClientId("GFSystemIterface");
            setClientSecret("Aa12345678900000!");
            setUsername("yaojinkang");
            setAccountId("2059620591685699584");
        }};
        ReceiptClaimResponse execute = cancelClaimRequest.execute(accessToken);
        String s = cancelClaimRequest.getClient().requestContent("/v2/gjwl/cas/cancelRecClaim/calcelrecclaiminterface", "xxlog", params, accessToken, null);

    }
}
