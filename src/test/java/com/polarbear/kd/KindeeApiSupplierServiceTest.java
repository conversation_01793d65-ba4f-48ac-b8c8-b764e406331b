package com.polarbear.kd;

import com.polarbear.kd.api.supplier.create.SupplierCreateResponse;
import com.polarbear.kd.api.supplier.unAudit.SupplierUnAuditResponse;
import com.polarbear.kd.core.KingdeeDefaultOpClient;
import org.junit.Test;


public class KindeeApiSupplierServiceTest  {

    @Test
    public void response() {
        String data = "{\n" +
                "\t\"data\":{\n" +
                "\t\t\"failCount\":\"1\",\n" +
                "\t\t\"result\":[\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"billIndex\":0,\n" +
                "\t\t\t\t\"billStatus\":false,\n" +
                "\t\t\t\t\"errors\":[\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"entityKey\":\"bd_supplier\",\n" +
                "\t\t\t\t\t\t\"entryRowIndex\":null,\n" +
                "\t\t\t\t\t\t\"keys\":{\n" +
                "\t\t\t\t\t\t\t\"number\":\"1111006\"\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"rowMsg\":[\"参数【createorg_number】必填。\",\"参数【establishdate】的类型为 Date，而传入值不能转换为该类型。\"],\n" +
                "\t\t\t\t\t\t\"subEntryRowIndex\":null\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t],\n" +
                "\t\t\t\t\"id\":null,\n" +
                "\t\t\t\t\"keys\":{\n" +
                "\t\t\t\t\t\"number\":\"1111006\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"number\":null,\n" +
                "\t\t\t\t\"type\":null\n" +
                "\t\t\t}\n" +
                "\t\t],\n" +
                "\t\t\"successCount\":\"0\"\n" +
                "\t},\n" +
                "\t\"errorCode\":\"400\",\n" +
                "\t\"message\":\"参数【createorg_number】必填。...等2处错误,TraceId：565021bf069adfe1\",\n" +
                "\t\"status\":false\n" +
                "}";

        SupplierCreateResponse response = KingdeeDefaultOpClient.instance().response(data, SupplierCreateResponse.class);
    }

    @Test
    public void responseAudit() {
        String data = "{\n" +
                "\t\"data\":{\n" +
                "\t\t\"failCount\":\"1\",\n" +
                "\t\t\"result\":[\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"billIndex\":0,\n" +
                "\t\t\t\t\"billStatus\":false,\n" +
                "\t\t\t\t\"errors\":[\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"entityKey\":\"bd_supplier\",\n" +
                "\t\t\t\t\t\t\"entryRowIndex\":null,\n" +
                "\t\t\t\t\t\t\"keys\":null,\n" +
                "\t\t\t\t\t\t\"rowMsg\":[\"覃恒宾正在PC端编辑该记录，请稍后再试或联系系统管理员。\"],\n" +
                "\t\t\t\t\t\t\"subEntryRowIndex\":null\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"entityKey\":\"bd_supplier\",\n" +
                "\t\t\t\t\t\t\"entryRowIndex\":null,\n" +
                "\t\t\t\t\t\t\"keys\":null,\n" +
                "\t\t\t\t\t\t\"rowMsg\":[\"只有暂存状态的数据才允许提交。\"],\n" +
                "\t\t\t\t\t\t\"subEntryRowIndex\":null\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t],\n" +
                "\t\t\t\t\"id\":\"2062321509803770880\",\n" +
                "\t\t\t\t\"keys\":{\n" +
                "\t\t\t\t\t\"number\":\"1111006\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"number\":null,\n" +
                "\t\t\t\t\"type\":\"Update\"\n" +
                "\t\t\t}\n" +
                "\t\t],\n" +
                "\t\t\"successCount\":\"0\"\n" +
                "\t},\n" +
                "\t\"errorCode\":\"603\",\n" +
                "\t\"message\":\"覃恒宾正在PC端编辑该记录，请稍后再试或联系系统管理员。...等2处错误\",\n" +
                "\t\"status\":false\n" +
                "}";

        SupplierUnAuditResponse response = KingdeeDefaultOpClient.instance().response(data, SupplierUnAuditResponse.class);
    }

    @Test
    public void responseAudit2() {
        String data = "{\n" +
                "\t\"data\":{\n" +
                "\t\t\"failCount\":\"1\",\n" +
                "\t\t\"result\":[\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"billIndex\":0,\n" +
                "\t\t\t\t\"billStatus\":false,\n" +
                "\t\t\t\t\"errors\":[\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"entityKey\":\"bd_supplier\",\n" +
                "\t\t\t\t\t\t\"entryRowIndex\":null,\n" +
                "\t\t\t\t\t\t\"keys\":null,\n" +
                "\t\t\t\t\t\t\"rowMsg\":[\"覃恒宾正在PC端编辑该记录，请稍后再试或联系系统管理员。\"],\n" +
                "\t\t\t\t\t\t\"subEntryRowIndex\":null\n" +
                "\t\t\t\t\t},\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"entityKey\":\"bd_supplier\",\n" +
                "\t\t\t\t\t\t\"entryRowIndex\":null,\n" +
                "\t\t\t\t\t\t\"keys\":null,\n" +
                "\t\t\t\t\t\t\"rowMsg\":[\"只有暂存状态的数据才允许提交。\"],\n" +
                "\t\t\t\t\t\t\"subEntryRowIndex\":null\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t],\n" +
                "\t\t\t\t\"id\":\"2062321509803770880\",\n" +
                "\t\t\t\t\"keys\":{\n" +
                "\t\t\t\t\t\"number\":\"1111006\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"number\":null,\n" +
                "\t\t\t\t\"type\":\"Update\"\n" +
                "\t\t\t}\n" +
                "\t\t],\n" +
                "\t\t\"successCount\":\"0\"\n" +
                "\t},\n" +
                "\t\"errorCode\":\"603\",\n" +
                "\t\"message\":\"覃恒宾正在PC端编辑该记录，请稍后再试或联系系统管理员。...等2处错误\",\n" +
                "\t\"status\":false\n" +
                "}";

        SupplierUnAuditResponse response = KingdeeDefaultOpClient.instance().response(data, SupplierUnAuditResponse.class);
    }
}