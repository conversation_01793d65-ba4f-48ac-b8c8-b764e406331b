package com.polarbear.kd;

import com.polarbear.kd.api.SimpleResponse;
import com.polarbear.kd.api.payment.response.AddKdPaymentResponse;
import com.polarbear.kd.api.payment.response.PayLineResultResponse;
import com.polarbear.kd.api.supplier.create.SupplierCreateResponse;
import com.polarbear.kd.core.KingdeeDefaultOpClient;
import org.junit.Test;


public class KindeeAddKdServiceTest extends BasicTest {

    @Test
    public void response() {
        String data = "{\n" +
                "\t\"data\":{\n" +
                "\t\t\"failCount\":\"1\",\n" +
                "\t\t\"result\":[\n" +
                "\t\t\t{\n" +
                "\t\t\t\t\"billIndex\":0,\n" +
                "\t\t\t\t\"billStatus\":false,\n" +
                "\t\t\t\t\"errors\":[\n" +
                "\t\t\t\t\t{\n" +
                "\t\t\t\t\t\t\"entityKey\":\"cas_paybill\",\n" +
                "\t\t\t\t\t\t\"entryRowIndex\":null,\n" +
                "\t\t\t\t\t\t\"keys\":{\n" +
                "\t\t\t\t\t\t\t\"billno\":\"212020\"\n" +
                "\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\"rowMsg\":[\"多类别基础资料类型字段“收款单位类型（基础资料类型）”未录入\"],\n" +
                "\t\t\t\t\t\t\"subEntryRowIndex\":null\n" +
                "\t\t\t\t\t}\n" +
                "\t\t\t\t],\n" +
                "\t\t\t\t\"id\":\"\",\n" +
                "\t\t\t\t\"keys\":{\n" +
                "\t\t\t\t\t\"billno\":\"212020\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"number\":null,\n" +
                "\t\t\t\t\"type\":\"Add\"\n" +
                "\t\t\t}\n" +
                "\t\t],\n" +
                "\t\t\"successCount\":\"0\"\n" +
                "\t},\n" +
                "\t\"errorCode\":\"603\",\n" +
                "\t\"message\":\"多类别基础资料类型字段“收款单位类型（基础资料类型）”未录入\",\n" +
                "\t\"status\":false\n" +
                "}";

        AddKdPaymentResponse response = KingdeeDefaultOpClient.instance().response(data, AddKdPaymentResponse.class);
    }


    @Test
    public void response2() {
        String data="{\n" +
                "\t\"data\":{\n" +
                "\t\t\"msg\":\"失败，查询参数为空\",\n" +
                "\t\t\"fail\":true,\n" +
                "\t\t\"code\":\"0\",\n" +
                "\t\t\"obj\":null,\n" +
                "\t\t\"sucess\":false,\n" +
                "\t\t\"submitValidateToken\":null,\n" +
                "\t\t\"resultlist\":[],\n" +
                "\t\t\"detailErrorMsgStack\":null\n" +
                "\t},\n" +
                "\t\"errorCode\":\"0\",\n" +
                "\t\"message\":null,\n" +
                "\t\"status\":true\n" +
                "}";
        PayLineResultResponse response = KingdeeDefaultOpClient.instance().response(data, PayLineResultResponse.class);
    }
}