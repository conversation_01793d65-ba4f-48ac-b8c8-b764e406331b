package com.polarbear.kd;


import com.polarbear.springframework.boot.common.model.TenantPrincipal;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class BasicTest {
    @Before
    public void setUp() {
        Authentication authentication = Mockito.mock(Authentication.class);
        TenantPrincipal tenantPrincipal = Mockito.mock(TenantPrincipal.class);
        when(tenantPrincipal.getTenantId()).thenReturn("0001");
        when(authentication.getPrincipal()).thenReturn(tenantPrincipal);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

}
