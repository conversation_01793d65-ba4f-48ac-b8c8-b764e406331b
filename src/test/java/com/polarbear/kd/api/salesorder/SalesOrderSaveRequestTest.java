package com.polarbear.kd.api.salesorder;

import com.polarbear.base.api.client.KingdeeApiBaseClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 销售订单保存接口测试
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@ExtendWith(MockitoExtension.class)
class SalesOrderSaveRequestTest {

    @Mock
    private KingdeeApiBaseClient kdClient;

    private SalesOrderSaveRequest request;

    @BeforeEach
    void setUp() {
        request = new SalesOrderSaveRequest();
    }

    @Test
    void testGetUrlPath() {
        assertEquals("/v2/gjwl/sm/sm_salorder/saveSalOrder", request.getUrlPath());
    }

    @Test
    void testLogModule() {
        assertEquals("kd.sm_salorder.saveSalOrder", request.logModule());
    }

    @Test
    void testGetResponseClass() {
        assertEquals(SalesOrderSaveResponse.class, request.getResponseClass());
    }

    @Test
    void testCreateSalesOrderRequest() {
        // 创建销售订单列表
        List<SalesOrder> salesOrders = createTestSalesOrders();
        
        // 设置请求参数
        request.setParam(salesOrders);
        
        // 验证参数设置
        assertNotNull(request.getParam());
        assertEquals(1, request.getParam().size());
        
        SalesOrder salesOrder = request.getParam().get(0);
        assertEquals("2025-08-19", salesOrder.getBizdate());
        assertEquals("1011", salesOrder.getOrgNumber());
        assertEquals("CUS-001", salesOrder.getCustomerNumber());
        assertEquals("sm_SalesOrder_STD_BT_S", salesOrder.getBilltypeNumber());
        assertEquals("210", salesOrder.getBiztypeNumber());
        assertEquals("CNY", salesOrder.getSettlecurrencyNumber());
        assertEquals("XSDD-2025-08-19-001", salesOrder.getGjwlThirdpartyBillno());
        assertEquals("广交云供应链管理系统", salesOrder.getGjwlSourcesystemtype());
        assertEquals("测试销售订单", salesOrder.getComment());
        assertTrue(salesOrder.getIstax());
        assertFalse(salesOrder.getIswholediscount());
        assertEquals(BigDecimal.ZERO, salesOrder.getWholediscountamount());
        assertEquals("SKTJ-1001_SYS", salesOrder.getRecconditionNumber());
        
        // 验证明细信息
        assertNotNull(salesOrder.getBillentry());
        assertEquals(1, salesOrder.getBillentry().size());
        
        SalesOrderEntry entry = salesOrder.getBillentry().get(0);
        assertEquals("010", entry.getLinetypeNumber());
        assertEquals("ITEM-001", entry.getMaterialNumber());
        assertEquals("pcs", entry.getUnitNumber());
        assertEquals("1011", entry.getEStockorgNumber());
        assertEquals("1011", entry.getEntrysettleorgNumber());
        assertEquals(new BigDecimal("100.00"), entry.getPrice());
        assertEquals(new BigDecimal("113.00"), entry.getPriceandtax());
        assertEquals(new BigDecimal("10"), entry.getQty());
        assertEquals("V5", entry.getTaxrateidNumber());
        assertEquals(new BigDecimal("13.00"), entry.getTaxamount());
        assertEquals("NULL", entry.getDiscounttype());
        assertEquals("测试明细", entry.getRemark());
        assertEquals("WH-001", entry.getWarehouseNumber());
        assertEquals(new BigDecimal("1130.00"), entry.getAmountandtax());
        assertEquals(new BigDecimal("1000.00"), entry.getAmount());
        assertFalse(entry.getIspresent());
    }

    /**
     * 创建测试用的销售订单数据
     */
    private List<SalesOrder> createTestSalesOrders() {
        List<SalesOrder> salesOrders = new ArrayList<>();
        SalesOrder salesOrder = new SalesOrder();

        // 设置销售订单主体信息
        salesOrder.setBizdate("2025-08-19");
        salesOrder.setOrgNumber("1011");
        salesOrder.setCustomerNumber("CUS-001");
        salesOrder.setBilltypeNumber("sm_SalesOrder_STD_BT_S");
        salesOrder.setBiztypeNumber("210");
        salesOrder.setSettlecurrencyNumber("CNY");
        salesOrder.setGjwlThirdpartyBillno("XSDD-2025-08-19-001");
        salesOrder.setGjwlSourcesystemtype("广交云供应链管理系统");
        salesOrder.setComment("测试销售订单");
        salesOrder.setIstax(true);
        salesOrder.setIswholediscount(false);
        salesOrder.setWholediscountamount(BigDecimal.ZERO);
        salesOrder.setRecconditionNumber("SKTJ-1001_SYS");

        // 创建销售订单明细列表
        List<SalesOrderEntry> entries = new ArrayList<>();
        SalesOrderEntry entry = new SalesOrderEntry();

        // 设置销售订单明细信息
        entry.setLinetypeNumber("010");
        entry.setMaterialNumber("ITEM-001");
        entry.setUnitNumber("pcs");
        entry.setEStockorgNumber("1011");
        entry.setEntrysettleorgNumber("1011");
        entry.setPrice(new BigDecimal("100.00"));
        entry.setPriceandtax(new BigDecimal("113.00"));
        entry.setQty(new BigDecimal("10"));
        entry.setTaxrateidNumber("V5");
        entry.setTaxamount(new BigDecimal("13.00"));
        entry.setDiscounttype("NULL");
        entry.setRemark("测试明细");
        entry.setWarehouseNumber("WH-001");
        entry.setAmountandtax(new BigDecimal("1130.00"));
        entry.setAmount(new BigDecimal("1000.00"));
        entry.setIspresent(false);

        entries.add(entry);
        salesOrder.setBillentry(entries);
        salesOrders.add(salesOrder);

        return salesOrders;
    }
}
