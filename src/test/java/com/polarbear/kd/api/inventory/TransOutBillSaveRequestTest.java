package com.polarbear.kd.api.inventory;

import com.polarbear.base.api.client.KingdeeApiBaseClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分步调出单保存接口测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class TransOutBillSaveRequestTest {

    @Mock
    private KingdeeApiBaseClient kdClient;

    private TransOutBillSaveRequest request;

    @BeforeEach
    void setUp() {
        request = new TransOutBillSaveRequest();
    }

    @Test
    void testGetUrlPath() {
        assertEquals("/v2/gjwl/im/im_transoutbill/SaveTransOutBill", request.getUrlPath());
    }

    @Test
    void testLogModule() {
        assertEquals("kd.im_transoutbill.SaveTransOutBill", request.logModule());
    }

    @Test
    void testGetResponseClass() {
        assertEquals(TransOutBillSaveResponse.class, request.getResponseClass());
    }

    @Test
    void testCreateTransOutBillWithRequiredFields() {
        // 创建分步调出单
        TransOutBill transOutBill = new TransOutBill();
        transOutBill.setGjwlThirdpartyBillno("DBCK-250822-000001");
        transOutBill.setGjwlSourcesystemtype("广交云供应链管理系统");
        transOutBill.setBiztime("2025-08-22");
        transOutBill.setTranstype("A");
        transOutBill.setOrgNumber("1011");
        transOutBill.setBilltypeNumber("im_AllotOutBill_STD_BT_S");
        transOutBill.setBiztypeNumber("310");
        transOutBill.setInvschemeNumber("315");
        transOutBill.setInorgNumber("1011");
        transOutBill.setSettlescurrencyNumber("CNY");

        // 创建明细
        List<TransOutBillEntry> entries = new ArrayList<>();
        TransOutBillEntry entry = new TransOutBillEntry();
        entry.setLinetypeNumber("010");
        entry.setMaterialNumber("Item-000000100");
        entry.setQty(new BigDecimal("20"));
        entry.setWarehouseNumber("CK-001");
        entry.setOutinvstatusNumber("110");
        entry.setOutinvtypeNumber("110");
        entry.setOutownertype("bos_org");
        entry.setOutownerNumber("1011");
        entry.setOutkeepertype("bos_org");
        entry.setOutkeeperNumber("1011");
        entry.setOwnertype("bos_org");
        entry.setKeepertype("bos_org");
        entry.setKeeperNumber("1011");
        entry.setInvstatusNumber("114");
        entry.setInvtypeNumber("110");

        entries.add(entry);
        transOutBill.setBillentry(entries);

        // 验证字段设置
        assertEquals("DBCK-250822-000001", transOutBill.getGjwlThirdpartyBillno());
        assertEquals("广交云供应链管理系统", transOutBill.getGjwlSourcesystemtype());
        assertEquals("2025-08-22", transOutBill.getBiztime());
        assertEquals("A", transOutBill.getTranstype());
        assertEquals("1011", transOutBill.getOrgNumber());
        assertEquals("im_AllotOutBill_STD_BT_S", transOutBill.getBilltypeNumber());
        assertEquals("310", transOutBill.getBiztypeNumber());
        assertEquals("315", transOutBill.getInvschemeNumber());
        assertEquals("1011", transOutBill.getInorgNumber());
        assertEquals("CNY", transOutBill.getSettlescurrencyNumber());
        
        assertNotNull(transOutBill.getBillentry());
        assertEquals(1, transOutBill.getBillentry().size());
        
        TransOutBillEntry testEntry = transOutBill.getBillentry().get(0);
        assertEquals("010", testEntry.getLinetypeNumber());
        assertEquals("Item-000000100", testEntry.getMaterialNumber());
        assertEquals(new BigDecimal("20"), testEntry.getQty());
        assertEquals("CK-001", testEntry.getWarehouseNumber());
        assertEquals("110", testEntry.getOutinvstatusNumber());
        assertEquals("110", testEntry.getOutinvtypeNumber());
        assertEquals("bos_org", testEntry.getOutownertype());
        assertEquals("1011", testEntry.getOutownerNumber());
        assertEquals("bos_org", testEntry.getOutkeepertype());
        assertEquals("1011", testEntry.getOutkeeperNumber());
    }

    @Test
    void testSetParam() {
        List<TransOutBill> transOutBills = new ArrayList<>();
        TransOutBill transOutBill = new TransOutBill();
        transOutBill.setGjwlThirdpartyBillno("TEST-001");
        transOutBills.add(transOutBill);

        request.setParam(transOutBills);
        
        assertEquals(transOutBills, request.getParam());
        assertEquals(1, request.getParam().size());
        assertEquals("TEST-001", request.getParam().get(0).getGjwlThirdpartyBillno());
    }
}
