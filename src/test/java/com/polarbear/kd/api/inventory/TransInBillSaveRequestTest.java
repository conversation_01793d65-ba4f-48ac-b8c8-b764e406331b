package com.polarbear.kd.api.inventory;

import com.polarbear.base.api.client.KingdeeApiBaseClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分步调入单保存接口测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class TransInBillSaveRequestTest {

    @Mock
    private KingdeeApiBaseClient kdClient;

    private TransInBillSaveRequest request;

    @BeforeEach
    void setUp() {
        request = new TransInBillSaveRequest();
    }

    @Test
    void testGetUrlPath() {
        assertEquals("/v2/im/im_transinbill/batchAdd_V2", request.getUrlPath());
    }

    @Test
    void testLogModule() {
        assertEquals("kd.im_transinbill.batchAdd_V2", request.logModule());
    }

    @Test
    void testGetResponseClass() {
        assertEquals(TransInBillSaveResponse.class, request.getResponseClass());
    }

    @Test
    void testCreateTransInBillWithRequiredFields() {
        // 创建分步调入单
        TransInBill transInBill = new TransInBill();
        transInBill.setOrgNumber("BU-001");
        transInBill.setGjwlThirdpartyBillno("DBRK-250731-000001");
        transInBill.setGjwlSourcesystemtype("广交云供应链管理系统");
        transInBill.setBiztime("2025-07-31");
        transInBill.setBilltypeNumber("im_AllotInBill_STD_BT_S");
        transInBill.setBiztypeNumber("310");
        transInBill.setInvschemeNumber("316");
        transInBill.setTranstype("A");
        transInBill.setTransit("A");
        transInBill.setOutorgNumber("BU-002");
        transInBill.setSettlescurrencyNumber("CNY");

        // 创建明细
        List<TransInBillEntry> entries = new ArrayList<>();
        TransInBillEntry entry = new TransInBillEntry();
        entry.setLinetypeNumber("010");
        entry.setMaterialNumber("Item-000000100");
        entry.setUnitNumber("pcs");
        entry.setQty(new BigDecimal("10"));
        entry.setWarehouseNumber("CK-001");
        entry.setInvstatusNumber("110");
        entry.setOwnertype("bos_org");
        entry.setOwnerNumber("BU-001");
        entry.setKeepertype("bos_org");
        entry.setKeeperNumber("BU-001");
        entry.setInvtypeNumber("110");
        entry.setOutinvstatusNumber("114");
        entry.setOutinvtypeNumber("110");
        entry.setOutownertype("bos_org");
        entry.setOutkeepertype("bos_org");
        entry.setOutownerNumber("BU-002");
        entry.setOutkeeperNumber("BU-002");
        entry.setOutwarehouseNumber("CK-002");

        entries.add(entry);
        transInBill.setBillentry(entries);

        // 验证字段设置
        assertEquals("BU-001", transInBill.getOrgNumber());
        assertEquals("DBRK-250731-000001", transInBill.getGjwlThirdpartyBillno());
        assertEquals("广交云供应链管理系统", transInBill.getGjwlSourcesystemtype());
        assertEquals("2025-07-31", transInBill.getBiztime());
        assertEquals("im_AllotInBill_STD_BT_S", transInBill.getBilltypeNumber());
        assertEquals("310", transInBill.getBiztypeNumber());
        assertEquals("316", transInBill.getInvschemeNumber());
        assertEquals("A", transInBill.getTranstype());
        assertEquals("A", transInBill.getTransit());
        assertEquals("BU-002", transInBill.getOutorgNumber());
        assertEquals("CNY", transInBill.getSettlescurrencyNumber());
        
        assertNotNull(transInBill.getBillentry());
        assertEquals(1, transInBill.getBillentry().size());
        
        TransInBillEntry testEntry = transInBill.getBillentry().get(0);
        assertEquals("010", testEntry.getLinetypeNumber());
        assertEquals("Item-000000100", testEntry.getMaterialNumber());
        assertEquals("pcs", testEntry.getUnitNumber());
        assertEquals(new BigDecimal("10"), testEntry.getQty());
        assertEquals("CK-001", testEntry.getWarehouseNumber());
        assertEquals("110", testEntry.getInvstatusNumber());
        assertEquals("bos_org", testEntry.getOwnertype());
        assertEquals("BU-001", testEntry.getOwnerNumber());
        assertEquals("bos_org", testEntry.getKeepertype());
        assertEquals("BU-001", testEntry.getKeeperNumber());
        assertEquals("110", testEntry.getInvtypeNumber());
        assertEquals("114", testEntry.getOutinvstatusNumber());
        assertEquals("110", testEntry.getOutinvtypeNumber());
        assertEquals("bos_org", testEntry.getOutownertype());
        assertEquals("bos_org", testEntry.getOutkeepertype());
        assertEquals("BU-002", testEntry.getOutownerNumber());
        assertEquals("BU-002", testEntry.getOutkeeperNumber());
        assertEquals("CK-002", testEntry.getOutwarehouseNumber());
    }

    @Test
    void testSetParam() {
        List<TransInBill> transInBills = new ArrayList<>();
        TransInBill transInBill = new TransInBill();
        transInBill.setGjwlThirdpartyBillno("TEST-001");
        transInBills.add(transInBill);

        request.setParam(transInBills);
        
        assertEquals(transInBills, request.getParam());
        assertEquals(1, request.getParam().size());
        assertEquals("TEST-001", request.getParam().get(0).getGjwlThirdpartyBillno());
    }
}
